{"ast": null, "code": "const checkStringStartsWith = token => key => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = checkStringStartsWith(\"--\");\nconst isCSSVariableToken = checkStringStartsWith(\"var(--\");\nconst cssVariableRegex = /var\\s*\\(\\s*--[\\w-]+(\\s*,\\s*(?:(?:[^)(]|\\((?:[^)(]+|\\([^)(]*\\))*\\))*)+)?\\s*\\)/g;\nexport { cssVariableRegex, isCSSVariableName, isCSSVariableToken };", "map": {"version": 3, "names": ["checkStringStartsWith", "token", "key", "startsWith", "isCSSVariableName", "isCSSVariableToken", "cssVariableRegex"], "sources": ["C:/Users/<USER>/Desktop/lightr or dead/dreamscape_ai/web_terminal/client/node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs"], "sourcesContent": ["const checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = checkStringStartsWith(\"--\");\nconst isCSSVariableToken = checkStringStartsWith(\"var(--\");\nconst cssVariableRegex = /var\\s*\\(\\s*--[\\w-]+(\\s*,\\s*(?:(?:[^)(]|\\((?:[^)(]+|\\([^)(]*\\))*\\))*)+)?\\s*\\)/g;\n\nexport { cssVariableRegex, isCSSVariableName, isCSSVariableToken };\n"], "mappings": "AAAA,MAAMA,qBAAqB,GAAIC,KAAK,IAAMC,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,UAAU,CAACF,KAAK,CAAC;AAClG,MAAMG,iBAAiB,GAAGJ,qBAAqB,CAAC,IAAI,CAAC;AACrD,MAAMK,kBAAkB,GAAGL,qBAAqB,CAAC,QAAQ,CAAC;AAC1D,MAAMM,gBAAgB,GAAG,+EAA+E;AAExG,SAASA,gBAAgB,EAAEF,iBAAiB,EAAEC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}