{"ast": null, "code": "import { isSVGComponent } from './is-svg-component.mjs';\nimport { createUseRender } from '../use-render.mjs';\nimport { svgMotionConfig } from '../../svg/config-motion.mjs';\nimport { htmlMotionConfig } from '../../html/config-motion.mjs';\nfunction createDomMotionConfig(Component, {\n  forwardMotionProps = false\n}, preloadedFeatures, createVisualElement) {\n  const baseConfig = isSVGComponent(Component) ? svgMotionConfig : htmlMotionConfig;\n  return {\n    ...baseConfig,\n    preloadedFeatures,\n    useRender: createUseRender(forwardMotionProps),\n    createVisualElement,\n    Component\n  };\n}\nexport { createDomMotionConfig };", "map": {"version": 3, "names": ["isSVGComponent", "createUseRender", "svgMotionConfig", "htmlMotionConfig", "createDomMotionConfig", "Component", "forwardMotionProps", "preloadedFeatures", "createVisualElement", "baseConfig", "useRender"], "sources": ["C:/Users/<USER>/Desktop/lightr or dead/dreamscape_ai/web_terminal/client/node_modules/framer-motion/dist/es/render/dom/utils/create-config.mjs"], "sourcesContent": ["import { isSVGComponent } from './is-svg-component.mjs';\nimport { createUseRender } from '../use-render.mjs';\nimport { svgMotionConfig } from '../../svg/config-motion.mjs';\nimport { htmlMotionConfig } from '../../html/config-motion.mjs';\n\nfunction createDomMotionConfig(Component, { forwardMotionProps = false }, preloadedFeatures, createVisualElement) {\n    const baseConfig = isSVGComponent(Component)\n        ? svgMotionConfig\n        : htmlMotionConfig;\n    return {\n        ...baseConfig,\n        preloadedFeatures,\n        useRender: createUseRender(forwardMotionProps),\n        createVisualElement,\n        Component,\n    };\n}\n\nexport { createDomMotionConfig };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,gBAAgB,QAAQ,8BAA8B;AAE/D,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EAAEC,kBAAkB,GAAG;AAAM,CAAC,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE;EAC9G,MAAMC,UAAU,GAAGT,cAAc,CAACK,SAAS,CAAC,GACtCH,eAAe,GACfC,gBAAgB;EACtB,OAAO;IACH,GAAGM,UAAU;IACbF,iBAAiB;IACjBG,SAAS,EAAET,eAAe,CAACK,kBAAkB,CAAC;IAC9CE,mBAAmB;IACnBH;EACJ,CAAC;AACL;AAEA,SAASD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}