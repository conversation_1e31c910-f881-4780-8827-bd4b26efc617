{"ast": null, "code": "import { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { frame, cancelFrame } from '../frameloop/frame.mjs';\nfunction useAnimationFrame(callback) {\n  const initialTimestamp = useRef(0);\n  const {\n    isStatic\n  } = useContext(MotionConfigContext);\n  useEffect(() => {\n    if (isStatic) return;\n    const provideTimeSinceStart = ({\n      timestamp,\n      delta\n    }) => {\n      if (!initialTimestamp.current) initialTimestamp.current = timestamp;\n      callback(timestamp - initialTimestamp.current, delta);\n    };\n    frame.update(provideTimeSinceStart, true);\n    return () => cancelFrame(provideTimeSinceStart);\n  }, [callback]);\n}\nexport { useAnimationFrame };", "map": {"version": 3, "names": ["useRef", "useContext", "useEffect", "MotionConfigContext", "frame", "cancelFrame", "useAnimationFrame", "callback", "initialTimestamp", "isStatic", "provideTimeSinceStart", "timestamp", "delta", "current", "update"], "sources": ["C:/Users/<USER>/Desktop/lightr or dead/dreamscape_ai/web_terminal/client/node_modules/framer-motion/dist/es/utils/use-animation-frame.mjs"], "sourcesContent": ["import { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { frame, cancelFrame } from '../frameloop/frame.mjs';\n\nfunction useAnimationFrame(callback) {\n    const initialTimestamp = useRef(0);\n    const { isStatic } = useContext(MotionConfigContext);\n    useEffect(() => {\n        if (isStatic)\n            return;\n        const provideTimeSinceStart = ({ timestamp, delta }) => {\n            if (!initialTimestamp.current)\n                initialTimestamp.current = timestamp;\n            callback(timestamp - initialTimestamp.current, delta);\n        };\n        frame.update(provideTimeSinceStart, true);\n        return () => cancelFrame(provideTimeSinceStart);\n    }, [callback]);\n}\n\nexport { useAnimationFrame };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACrD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;AAE3D,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACjC,MAAMC,gBAAgB,GAAGR,MAAM,CAAC,CAAC,CAAC;EAClC,MAAM;IAAES;EAAS,CAAC,GAAGR,UAAU,CAACE,mBAAmB,CAAC;EACpDD,SAAS,CAAC,MAAM;IACZ,IAAIO,QAAQ,EACR;IACJ,MAAMC,qBAAqB,GAAGA,CAAC;MAAEC,SAAS;MAAEC;IAAM,CAAC,KAAK;MACpD,IAAI,CAACJ,gBAAgB,CAACK,OAAO,EACzBL,gBAAgB,CAACK,OAAO,GAAGF,SAAS;MACxCJ,QAAQ,CAACI,SAAS,GAAGH,gBAAgB,CAACK,OAAO,EAAED,KAAK,CAAC;IACzD,CAAC;IACDR,KAAK,CAACU,MAAM,CAACJ,qBAAqB,EAAE,IAAI,CAAC;IACzC,OAAO,MAAML,WAAW,CAACK,qBAAqB,CAAC;EACnD,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;AAClB;AAEA,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}