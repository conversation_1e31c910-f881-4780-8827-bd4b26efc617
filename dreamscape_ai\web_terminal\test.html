<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - DreamScape AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #000;
            color: #0f0;
            padding: 20px;
            text-align: center;
        }
        .status {
            font-size: 24px;
            margin: 20px 0;
        }
        .info {
            background: rgba(0,255,0,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🌙 DreamScape AI Test</h1>
    <div class="status" id="status">Testování připojení...</div>
    
    <div class="info">
        <h3>📱 Mobilní přístup:</h3>
        <p><strong>Hlavní aplikace:</strong><br>
        <a href="http://*************:3001" style="color: #0f0;">http://*************:3001</a></p>
        
        <p><strong><PERSON><PERSON><PERSON><PERSON> verze:</strong><br>
        <a href="http://*************:3001/mobile" style="color: #0f0;">http://*************:3001/mobile</a></p>
        
        <p><strong>React dev server:</strong><br>
        <a href="http://*************:3000" style="color: #0f0;">http://*************:3000</a></p>
    </div>
    
    <div class="info">
        <h3>🔧 Pokud nefunguje:</h3>
        <p>1. Zkontroluj, že jsi na stejné WiFi síti</p>
        <p>2. Zkus vypnout/zapnout WiFi na mobilu</p>
        <p>3. Zkus jiný prohlížeč na mobilu</p>
        <p>4. Zkus přístup přes hotspot</p>
    </div>
    
    <script>
        document.getElementById('status').innerHTML = '✅ Test stránka funguje!<br>Server je dostupný na síti.';
    </script>
</body>
</html>
