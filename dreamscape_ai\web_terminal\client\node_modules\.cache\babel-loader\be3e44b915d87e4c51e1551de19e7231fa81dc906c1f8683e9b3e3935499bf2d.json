{"ast": null, "code": "import { invariant } from '../../utils/errors.mjs';\nimport * as React from 'react';\nimport { forwardRef, useRef, useEffect } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { checkReorder } from './utils/check-reorder.mjs';\nfunction ReorderGroup({\n  children,\n  as = \"ul\",\n  axis = \"y\",\n  onReorder,\n  values,\n  ...props\n}, externalRef) {\n  const Component = useConstant(() => motion(as));\n  const order = [];\n  const isReordering = useRef(false);\n  invariant(Boolean(values), \"Reorder.Group must be provided a values prop\");\n  const context = {\n    axis,\n    registerItem: (value, layout) => {\n      // If the entry was already added, update it rather than adding it again\n      const idx = order.findIndex(entry => value === entry.value);\n      if (idx !== -1) {\n        order[idx].layout = layout[axis];\n      } else {\n        order.push({\n          value: value,\n          layout: layout[axis]\n        });\n      }\n      order.sort(compareMin);\n    },\n    updateOrder: (item, offset, velocity) => {\n      if (isReordering.current) return;\n      const newOrder = checkReorder(order, item, offset, velocity);\n      if (order !== newOrder) {\n        isReordering.current = true;\n        onReorder(newOrder.map(getValue).filter(value => values.indexOf(value) !== -1));\n      }\n    }\n  };\n  useEffect(() => {\n    isReordering.current = false;\n  });\n  return React.createElement(Component, {\n    ...props,\n    ref: externalRef,\n    ignoreStrict: true\n  }, React.createElement(ReorderContext.Provider, {\n    value: context\n  }, children));\n}\nconst Group = forwardRef(ReorderGroup);\nfunction getValue(item) {\n  return item.value;\n}\nfunction compareMin(a, b) {\n  return a.layout.min - b.layout.min;\n}\nexport { Group, ReorderGroup };", "map": {"version": 3, "names": ["invariant", "React", "forwardRef", "useRef", "useEffect", "ReorderContext", "motion", "useConstant", "check<PERSON>eor<PERSON>", "ReorderGroup", "children", "as", "axis", "onReorder", "values", "props", "externalRef", "Component", "order", "isReordering", "Boolean", "context", "registerItem", "value", "layout", "idx", "findIndex", "entry", "push", "sort", "compareMin", "updateOrder", "item", "offset", "velocity", "current", "newOrder", "map", "getValue", "filter", "indexOf", "createElement", "ref", "ignoreStrict", "Provider", "Group", "a", "b", "min"], "sources": ["C:/Users/<USER>/Desktop/lightr or dead/dreamscape_ai/web_terminal/client/node_modules/framer-motion/dist/es/components/Reorder/Group.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport * as React from 'react';\nimport { forwardRef, useRef, useEffect } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { checkReorder } from './utils/check-reorder.mjs';\n\nfunction ReorderGroup({ children, as = \"ul\", axis = \"y\", onReorder, values, ...props }, externalRef) {\n    const Component = useConstant(() => motion(as));\n    const order = [];\n    const isReordering = useRef(false);\n    invariant(Boolean(values), \"Reorder.Group must be provided a values prop\");\n    const context = {\n        axis,\n        registerItem: (value, layout) => {\n            // If the entry was already added, update it rather than adding it again\n            const idx = order.findIndex((entry) => value === entry.value);\n            if (idx !== -1) {\n                order[idx].layout = layout[axis];\n            }\n            else {\n                order.push({ value: value, layout: layout[axis] });\n            }\n            order.sort(compareMin);\n        },\n        updateOrder: (item, offset, velocity) => {\n            if (isReordering.current)\n                return;\n            const newOrder = checkReorder(order, item, offset, velocity);\n            if (order !== newOrder) {\n                isReordering.current = true;\n                onReorder(newOrder\n                    .map(getValue)\n                    .filter((value) => values.indexOf(value) !== -1));\n            }\n        },\n    };\n    useEffect(() => {\n        isReordering.current = false;\n    });\n    return (React.createElement(Component, { ...props, ref: externalRef, ignoreStrict: true },\n        React.createElement(ReorderContext.Provider, { value: context }, children)));\n}\nconst Group = forwardRef(ReorderGroup);\nfunction getValue(item) {\n    return item.value;\n}\nfunction compareMin(a, b) {\n    return a.layout.min - b.layout.min;\n}\n\nexport { Group, ReorderGroup };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACrD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,6BAA6B;AACpD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AAExD,SAASC,YAAYA,CAAC;EAAEC,QAAQ;EAAEC,EAAE,GAAG,IAAI;EAAEC,IAAI,GAAG,GAAG;EAAEC,SAAS;EAAEC,MAAM;EAAE,GAAGC;AAAM,CAAC,EAAEC,WAAW,EAAE;EACjG,MAAMC,SAAS,GAAGV,WAAW,CAAC,MAAMD,MAAM,CAACK,EAAE,CAAC,CAAC;EAC/C,MAAMO,KAAK,GAAG,EAAE;EAChB,MAAMC,YAAY,GAAGhB,MAAM,CAAC,KAAK,CAAC;EAClCH,SAAS,CAACoB,OAAO,CAACN,MAAM,CAAC,EAAE,8CAA8C,CAAC;EAC1E,MAAMO,OAAO,GAAG;IACZT,IAAI;IACJU,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7B;MACA,MAAMC,GAAG,GAAGP,KAAK,CAACQ,SAAS,CAAEC,KAAK,IAAKJ,KAAK,KAAKI,KAAK,CAACJ,KAAK,CAAC;MAC7D,IAAIE,GAAG,KAAK,CAAC,CAAC,EAAE;QACZP,KAAK,CAACO,GAAG,CAAC,CAACD,MAAM,GAAGA,MAAM,CAACZ,IAAI,CAAC;MACpC,CAAC,MACI;QACDM,KAAK,CAACU,IAAI,CAAC;UAAEL,KAAK,EAAEA,KAAK;UAAEC,MAAM,EAAEA,MAAM,CAACZ,IAAI;QAAE,CAAC,CAAC;MACtD;MACAM,KAAK,CAACW,IAAI,CAACC,UAAU,CAAC;IAC1B,CAAC;IACDC,WAAW,EAAEA,CAACC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,KAAK;MACrC,IAAIf,YAAY,CAACgB,OAAO,EACpB;MACJ,MAAMC,QAAQ,GAAG5B,YAAY,CAACU,KAAK,EAAEc,IAAI,EAAEC,MAAM,EAAEC,QAAQ,CAAC;MAC5D,IAAIhB,KAAK,KAAKkB,QAAQ,EAAE;QACpBjB,YAAY,CAACgB,OAAO,GAAG,IAAI;QAC3BtB,SAAS,CAACuB,QAAQ,CACbC,GAAG,CAACC,QAAQ,CAAC,CACbC,MAAM,CAAEhB,KAAK,IAAKT,MAAM,CAAC0B,OAAO,CAACjB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;MACzD;IACJ;EACJ,CAAC;EACDnB,SAAS,CAAC,MAAM;IACZe,YAAY,CAACgB,OAAO,GAAG,KAAK;EAChC,CAAC,CAAC;EACF,OAAQlC,KAAK,CAACwC,aAAa,CAACxB,SAAS,EAAE;IAAE,GAAGF,KAAK;IAAE2B,GAAG,EAAE1B,WAAW;IAAE2B,YAAY,EAAE;EAAK,CAAC,EACrF1C,KAAK,CAACwC,aAAa,CAACpC,cAAc,CAACuC,QAAQ,EAAE;IAAErB,KAAK,EAAEF;EAAQ,CAAC,EAAEX,QAAQ,CAAC,CAAC;AACnF;AACA,MAAMmC,KAAK,GAAG3C,UAAU,CAACO,YAAY,CAAC;AACtC,SAAS6B,QAAQA,CAACN,IAAI,EAAE;EACpB,OAAOA,IAAI,CAACT,KAAK;AACrB;AACA,SAASO,UAAUA,CAACgB,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOD,CAAC,CAACtB,MAAM,CAACwB,GAAG,GAAGD,CAAC,CAACvB,MAAM,CAACwB,GAAG;AACtC;AAEA,SAASH,KAAK,EAAEpC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}