{"ast": null, "code": "import { useAnimationFrame } from '../utils/use-animation-frame.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nfunction useTime() {\n  const time = useMotionValue(0);\n  useAnimationFrame(t => time.set(t));\n  return time;\n}\nexport { useTime };", "map": {"version": 3, "names": ["useAnimationFrame", "useMotionValue", "useTime", "time", "t", "set"], "sources": ["C:/Users/<USER>/Desktop/lightr or dead/dreamscape_ai/web_terminal/client/node_modules/framer-motion/dist/es/value/use-time.mjs"], "sourcesContent": ["import { useAnimationFrame } from '../utils/use-animation-frame.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\n\nfunction useTime() {\n    const time = useMotionValue(0);\n    useAnimationFrame((t) => time.set(t));\n    return time;\n}\n\nexport { useTime };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,cAAc,QAAQ,wBAAwB;AAEvD,SAASC,OAAOA,CAAA,EAAG;EACf,MAAMC,IAAI,GAAGF,cAAc,CAAC,CAAC,CAAC;EAC9BD,iBAAiB,CAAEI,CAAC,IAAKD,IAAI,CAACE,GAAG,CAACD,CAAC,CAAC,CAAC;EACrC,OAAOD,IAAI;AACf;AAEA,SAASD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}