{"ast": null, "code": "import { noop } from '../utils/noop.mjs';\nimport { createRenderBatcher } from './batcher.mjs';\nconst {\n  schedule: frame,\n  cancel: cancelFrame,\n  state: frameData,\n  steps\n} = createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\nexport { cancelFrame, frame, frameData, steps };", "map": {"version": 3, "names": ["noop", "createRenderBatcher", "schedule", "frame", "cancel", "cancelFrame", "state", "frameData", "steps", "requestAnimationFrame"], "sources": ["C:/Users/<USER>/Desktop/lightr or dead/dreamscape_ai/web_terminal/client/node_modules/framer-motion/dist/es/frameloop/frame.mjs"], "sourcesContent": ["import { noop } from '../utils/noop.mjs';\nimport { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps, } = createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\n\nexport { cancelFrame, frame, frameData, steps };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,mBAAmB;AACxC,SAASC,mBAAmB,QAAQ,eAAe;AAEnD,MAAM;EAAEC,QAAQ,EAAEC,KAAK;EAAEC,MAAM,EAAEC,WAAW;EAAEC,KAAK,EAAEC,SAAS;EAAEC;AAAO,CAAC,GAAGP,mBAAmB,CAAC,OAAOQ,qBAAqB,KAAK,WAAW,GAAGA,qBAAqB,GAAGT,IAAI,EAAE,IAAI,CAAC;AAEjL,SAASK,WAAW,EAAEF,KAAK,EAAEI,SAAS,EAAEC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}