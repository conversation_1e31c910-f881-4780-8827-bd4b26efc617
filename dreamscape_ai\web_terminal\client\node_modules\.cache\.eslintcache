[{"C:\\Users\\<USER>\\Desktop\\lightr or dead\\dreamscape_ai\\web_terminal\\client\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\lightr or dead\\dreamscape_ai\\web_terminal\\client\\src\\App.tsx": "2"}, {"size": 255, "mtime": 1749010971737, "results": "3", "hashOfConfig": "4"}, {"size": 16867, "mtime": 1749010419111, "results": "5", "hashOfConfig": "4"}, {"filePath": "6", "messages": "7", "suppressedMessages": "8", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6fsd0o", {"filePath": "9", "messages": "10", "suppressedMessages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\lightr or dead\\dreamscape_ai\\web_terminal\\client\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\lightr or dead\\dreamscape_ai\\web_terminal\\client\\src\\App.tsx", ["12"], [], {"ruleId": "13", "severity": 1, "message": "14", "line": 534, "column": 9, "nodeType": "15", "messageId": "16", "endLine": 534, "endColumn": 28}, "@typescript-eslint/no-unused-vars", "'handle3DInteraction' is assigned a value but never used.", "Identifier", "unusedVar"]