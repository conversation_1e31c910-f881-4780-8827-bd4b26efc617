/**
 * DreamScape AI - Modern Terminal Web Server
 * Express server with Socket.io for real-time AI dream analysis
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const app = express();
const server = http.createServer(app);

// Socket.io setup with CORS for mobile access
const io = socketIo(server, {
  cors: {
    origin: "*", // Allow all origins for development
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    credentials: true,
    allowedHeaders: ["Content-Type", "Authorization"]
  },
  allowEIO3: true,
  transports: ['websocket', 'polling']
});

const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet({
  contentSecurityPolicy: false // Disable for development
}));
app.use(compression());
app.use(morgan('combined'));
app.use(cors({
  origin: "*", // Allow all origins for mobile access
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  optionsSuccessStatus: 200
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, 'client/build')));

// AI Dream Analyzer Class
class ModernDreamAnalyzer {
  constructor() {
    this.dreamSymbols = {
      'flying': {
        meanings: ['freedom', 'escape', 'transcendence', 'control'],
        emotions: ['excitement', 'fear', 'liberation'],
        colors: ['sky_blue', 'white', 'light_purple'],
        elements3d: ['wings', 'clouds', 'open_sky', 'wind_effects']
      },
      'water': {
        meanings: ['emotions', 'subconscious', 'purification', 'life_force'],
        emotions: ['calm', 'fear', 'cleansing'],
        colors: ['blue', 'cyan', 'deep_blue', 'turquoise'],
        elements3d: ['waves', 'ripples', 'underwater', 'rain']
      },
      'falling': {
        meanings: ['loss_of_control', 'anxiety', 'failure', 'letting_go'],
        emotions: ['anxiety', 'fear', 'helplessness'],
        colors: ['dark_red', 'black', 'gray'],
        elements3d: ['void', 'spinning', 'gravity_effects', 'vertigo']
      },
      'house': {
        meanings: ['self', 'psyche', 'security', 'family'],
        emotions: ['comfort', 'nostalgia', 'safety'],
        colors: ['warm_brown', 'yellow', 'orange'],
        elements3d: ['rooms', 'doors', 'windows', 'furniture']
      },
      'animals': {
        meanings: ['instincts', 'nature', 'wild_self', 'guidance'],
        emotions: ['connection', 'fear', 'wonder'],
        colors: ['natural_green', 'brown', 'earth_tones'],
        elements3d: ['creatures', 'movement', 'natural_behavior', 'sounds']
      },
      'car': {
        meanings: ['life_direction', 'control', 'journey', 'progress'],
        emotions: ['control', 'anxiety', 'movement'],
        colors: ['metallic', 'red', 'black'],
        elements3d: ['vehicle', 'road', 'speed', 'steering']
      },
      'death': {
        meanings: ['transformation', 'ending', 'rebirth', 'fear'],
        emotions: ['fear', 'sadness', 'acceptance'],
        colors: ['black', 'white', 'purple'],
        elements3d: ['shadows', 'light_transition', 'ethereal', 'mist']
      },
      'fire': {
        meanings: ['passion', 'destruction', 'purification', 'energy'],
        emotions: ['passion', 'anger', 'energy'],
        colors: ['red', 'orange', 'yellow'],
        elements3d: ['flames', 'heat_effects', 'smoke', 'sparks']
      }
    };

    this.emotionKeywords = {
      'fear': ['afraid', 'scared', 'terrified', 'frightened', 'panic', 'horror', 'dread'],
      'joy': ['happy', 'excited', 'joyful', 'elated', 'cheerful', 'delighted', 'blissful'],
      'sadness': ['sad', 'depressed', 'melancholy', 'grief', 'sorrow', 'crying', 'tears'],
      'anger': ['angry', 'furious', 'rage', 'mad', 'irritated', 'frustrated', 'hostile'],
      'love': ['love', 'affection', 'romance', 'caring', 'tender', 'passionate', 'adoration'],
      'anxiety': ['anxious', 'worried', 'nervous', 'stressed', 'tense', 'uneasy', 'restless'],
      'surprise': ['surprised', 'shocked', 'amazed', 'astonished', 'startled', 'stunned'],
      'confusion': ['confused', 'lost', 'bewildered', 'puzzled', 'disoriented', 'unclear'],
      'peace': ['peaceful', 'calm', 'serene', 'tranquil', 'relaxed', 'content'],
      'excitement': ['excited', 'thrilled', 'energetic', 'enthusiastic', 'pumped']
    };
  }

  async analyzeDream(dreamText, sessionId) {
    const startTime = Date.now();
    const dreamId = uuidv4();
    
    console.log(`🌙 Analyzing dream for session ${sessionId}: "${dreamText.substring(0, 50)}..."`);
    
    // Clean and prepare text
    const cleanText = dreamText.toLowerCase().trim();
    
    // Extract emotions
    const emotions = this.extractEmotions(cleanText);
    
    // Extract symbols
    const symbols = this.extractSymbols(cleanText);
    
    // Determine environment
    const environment = this.determineEnvironment(cleanText);
    
    // Generate 3D scene data
    const scene3D = this.generate3DScene(emotions, symbols, environment);
    
    // Generate insights
    const insights = this.generateInsights(emotions, symbols, cleanText);
    
    // Calculate confidence
    const confidence = this.calculateConfidence(emotions, symbols, cleanText);
    
    const processingTime = Date.now() - startTime;
    
    const analysis = {
      dreamId,
      sessionId,
      timestamp: new Date().toISOString(),
      originalText: dreamText,
      emotions,
      symbols,
      environment,
      scene3D,
      insights,
      confidence,
      processingTime,
      ready: true
    };
    
    console.log(`✅ Analysis complete in ${processingTime}ms with ${confidence}% confidence`);
    return analysis;
  }

  extractEmotions(text) {
    const emotions = {};
    let totalScore = 0;
    
    for (const [emotion, keywords] of Object.entries(this.emotionKeywords)) {
      let score = 0;
      keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = text.match(regex);
        if (matches) {
          score += matches.length * 0.2;
        }
      });
      
      if (score > 0) {
        emotions[emotion] = Math.min(1, score);
        totalScore += score;
      }
    }
    
    // Normalize scores
    if (totalScore > 0) {
      for (const emotion in emotions) {
        emotions[emotion] = emotions[emotion] / totalScore;
      }
    } else {
      emotions['neutral'] = 1.0;
    }
    
    return emotions;
  }

  extractSymbols(text) {
    const foundSymbols = [];
    
    for (const [symbol, data] of Object.entries(this.dreamSymbols)) {
      const regex = new RegExp(`\\b${symbol}\\b`, 'gi');
      if (text.match(regex) || data.meanings.some(meaning => text.includes(meaning))) {
        foundSymbols.push({
          name: symbol,
          meanings: data.meanings,
          emotions: data.emotions,
          colors: data.colors,
          elements3D: data.elements3d,
          importance: 0.7 + Math.random() * 0.3
        });
      }
    }
    
    // Add contextual symbols based on keywords
    const contextualSymbols = {
      'night': { name: 'darkness', meanings: ['unknown', 'mystery', 'subconscious'] },
      'light': { name: 'illumination', meanings: ['hope', 'knowledge', 'guidance'] },
      'forest': { name: 'nature', meanings: ['growth', 'unknown', 'natural_self'] },
      'city': { name: 'civilization', meanings: ['society', 'complexity', 'progress'] }
    };
    
    for (const [keyword, symbolData] of Object.entries(contextualSymbols)) {
      if (text.includes(keyword) && !foundSymbols.some(s => s.name === symbolData.name)) {
        foundSymbols.push({
          ...symbolData,
          colors: ['contextual'],
          elements3D: ['environmental'],
          importance: 0.5
        });
      }
    }
    
    return foundSymbols.length > 0 ? foundSymbols : [{
      name: 'general_dream',
      meanings: ['subconscious_processing', 'memory_consolidation'],
      colors: ['neutral'],
      elements3D: ['abstract'],
      importance: 0.3
    }];
  }

  determineEnvironment(text) {
    const environments = {
      'nature': ['forest', 'tree', 'mountain', 'ocean', 'field', 'garden', 'river'],
      'urban': ['city', 'street', 'building', 'office', 'store', 'traffic'],
      'indoor': ['house', 'room', 'bedroom', 'kitchen', 'bathroom', 'basement'],
      'sky': ['flying', 'clouds', 'air', 'sky', 'above'],
      'underground': ['cave', 'tunnel', 'basement', 'underground'],
      'water': ['ocean', 'sea', 'lake', 'river', 'swimming', 'underwater'],
      'abstract': ['void', 'space', 'dimension', 'surreal', 'impossible']
    };
    
    for (const [env, keywords] of Object.entries(environments)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return env;
      }
    }
    
    return 'abstract';
  }

  generate3DScene(emotions, symbols, environment) {
    const dominantEmotion = Object.keys(emotions)[0] || 'neutral';
    const primarySymbol = symbols[0];
    
    // Color palette based on emotions and symbols
    let colorPalette = ['#4a90e2', '#7b68ee', '#50c878']; // Default
    
    if (primarySymbol && primarySymbol.colors) {
      const colorMap = {
        'blue': '#4a90e2', 'red': '#e74c3c', 'green': '#50c878',
        'purple': '#7b68ee', 'yellow': '#f1c40f', 'orange': '#e67e22',
        'black': '#2c3e50', 'white': '#ecf0f1', 'gray': '#95a5a6'
      };
      
      colorPalette = primarySymbol.colors.map(color => 
        colorMap[color.split('_')[0]] || colorMap['blue']
      );
    }
    
    // Lighting based on emotions
    const lightingMap = {
      'fear': { intensity: 0.3, color: '#8b0000', shadows: true },
      'joy': { intensity: 1.2, color: '#ffd700', shadows: false },
      'sadness': { intensity: 0.5, color: '#4682b4', shadows: true },
      'anger': { intensity: 0.8, color: '#dc143c', shadows: true },
      'peace': { intensity: 0.7, color: '#98fb98', shadows: false },
      'neutral': { intensity: 0.8, color: '#ffffff', shadows: false }
    };
    
    const lighting = lightingMap[dominantEmotion] || lightingMap['neutral'];
    
    // 3D Elements to render
    const elements3D = [];
    symbols.forEach(symbol => {
      if (symbol.elements3D) {
        elements3D.push(...symbol.elements3D);
      }
    });
    
    // Environment-specific elements
    const environmentElements = {
      'nature': ['trees', 'grass', 'rocks', 'sky'],
      'urban': ['buildings', 'roads', 'vehicles', 'lights'],
      'indoor': ['walls', 'furniture', 'doors', 'windows'],
      'sky': ['clouds', 'atmosphere', 'horizon'],
      'water': ['waves', 'reflections', 'underwater_effects'],
      'abstract': ['geometric_shapes', 'particles', 'void']
    };
    
    elements3D.push(...(environmentElements[environment] || environmentElements['abstract']));
    
    return {
      environment,
      colorPalette,
      lighting,
      elements3D: [...new Set(elements3D)], // Remove duplicates
      atmosphere: {
        fog: dominantEmotion === 'fear' || dominantEmotion === 'sadness',
        particles: dominantEmotion === 'joy' || dominantEmotion === 'excitement',
        weather: this.determineWeather(dominantEmotion)
      },
      camera: {
        position: environment === 'sky' ? 'elevated' : 'ground',
        movement: symbols.some(s => s.name === 'flying') ? 'flying' : 'walking'
      },
      audio: {
        ambient: this.generateAmbientSounds(environment, dominantEmotion),
        effects: symbols.map(s => s.name + '_sound')
      }
    };
  }

  determineWeather(emotion) {
    const weatherMap = {
      'fear': 'stormy',
      'sadness': 'rainy',
      'anger': 'thunderstorm',
      'joy': 'sunny',
      'peace': 'clear',
      'neutral': 'partly_cloudy'
    };
    
    return weatherMap[emotion] || 'clear';
  }

  generateAmbientSounds(environment, emotion) {
    const environmentSounds = {
      'nature': ['birds', 'wind', 'leaves'],
      'urban': ['traffic', 'city_hum', 'footsteps'],
      'indoor': ['clock', 'air_conditioning', 'distant_voices'],
      'sky': ['wind', 'air_movement', 'distant_sounds'],
      'water': ['waves', 'water_movement', 'bubbles'],
      'abstract': ['ethereal_tones', 'void_echoes']
    };
    
    const emotionalSounds = {
      'fear': ['tension_music', 'heartbeat'],
      'joy': ['uplifting_music', 'cheerful_tones'],
      'sadness': ['melancholy_music', 'soft_rain'],
      'peace': ['meditation_music', 'gentle_breeze']
    };
    
    return [
      ...(environmentSounds[environment] || []),
      ...(emotionalSounds[emotion] || [])
    ];
  }

  generateInsights(emotions, symbols, text) {
    const dominantEmotion = Object.keys(emotions)[0];
    const primarySymbol = symbols[0];
    
    const insights = {
      emotional: `Your dream shows strong ${dominantEmotion} themes, suggesting current emotional processing around ${dominantEmotion === 'fear' ? 'challenges or uncertainties' : dominantEmotion === 'joy' ? 'positive experiences' : 'life transitions'}.`,
      symbolic: primarySymbol ? `The presence of '${primarySymbol.name}' suggests themes of ${primarySymbol.meanings.slice(0, 2).join(' and ')}.` : 'General dream processing detected.',
      psychological: this.generatePsychologicalInsight(emotions, symbols),
      recommendation: this.generateRecommendation(dominantEmotion, symbols)
    };
    
    return insights;
  }

  generatePsychologicalInsight(emotions, symbols) {
    const hasControlSymbols = symbols.some(s => ['flying', 'car', 'falling'].includes(s.name));
    const hasRelationshipSymbols = symbols.some(s => ['house', 'animals', 'people'].includes(s.name));
    
    if (hasControlSymbols) {
      return 'This dream appears to be processing themes of control and autonomy in your life.';
    } else if (hasRelationshipSymbols) {
      return 'This dream seems to focus on relationships and social connections.';
    } else {
      return 'This dream appears to be processing recent experiences and integrating them into memory.';
    }
  }

  generateRecommendation(emotion, symbols) {
    if (emotion === 'fear' || emotion === 'anxiety') {
      return 'Consider relaxation techniques before sleep and reflect on current stressors.';
    } else if (emotion === 'sadness') {
      return 'This dream may be helping process difficult emotions. Consider journaling or talking to someone.';
    } else if (emotion === 'joy') {
      return 'Your positive dream energy suggests good mental state. Continue current positive practices.';
    } else {
      return 'Regular dream journaling can help you better understand your subconscious patterns.';
    }
  }

  calculateConfidence(emotions, symbols, text) {
    let confidence = 0;
    
    // Text length factor
    const wordCount = text.split(' ').length;
    confidence += Math.min(30, wordCount * 2);
    
    // Emotion detection factor
    confidence += Object.keys(emotions).length * 15;
    
    // Symbol detection factor
    confidence += symbols.length * 20;
    
    // Specific keyword factor
    const specificKeywords = ['dream', 'saw', 'felt', 'was', 'went', 'found'];
    const keywordCount = specificKeywords.filter(keyword => text.includes(keyword)).length;
    confidence += keywordCount * 5;
    
    return Math.min(100, Math.max(20, confidence));
  }
}

// Initialize AI analyzer
const dreamAnalyzer = new ModernDreamAnalyzer();

// Store active sessions
const activeSessions = new Map();

// Mobile route
app.get('/mobile', (req, res) => {
  res.sendFile(path.join(__dirname, 'mobile.html'));
});

// Test route
app.get('/test', (req, res) => {
  res.sendFile(path.join(__dirname, 'test.html'));
});

// API Routes
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    activeConnections: io.engine.clientsCount
  });
});

app.get('/api/commands', (req, res) => {
  res.json({
    commands: [
      { cmd: 'help', desc: 'Show available commands' },
      { cmd: 'analyze <dream>', desc: 'Analyze a dream description' },
      { cmd: 'samples', desc: 'Show sample dreams' },
      { cmd: 'clear', desc: 'Clear terminal' },
      { cmd: 'status', desc: 'Show system status' },
      { cmd: '3d', desc: 'Toggle 3D visualization' }
    ]
  });
});

// Socket.io connection handling
io.on('connection', (socket) => {
  const sessionId = uuidv4();
  activeSessions.set(sessionId, {
    socketId: socket.id,
    connectedAt: new Date(),
    dreamCount: 0
  });
  
  console.log(`🔌 New connection: ${socket.id} (Session: ${sessionId})`);
  
  // Send welcome message
  socket.emit('terminal_output', {
    type: 'system',
    content: `🌙 Welcome to DreamScape AI Terminal v2.0\nSession ID: ${sessionId}\nType 'help' for commands or start with 'analyze <your dream>'`,
    timestamp: new Date().toISOString()
  });
  
  // Handle command input
  socket.on('command_input', async (data) => {
    const { command, args } = data;
    const session = activeSessions.get(sessionId);
    
    console.log(`📝 Command from ${sessionId}: ${command} ${args || ''}`);
    
    try {
      switch (command.toLowerCase()) {
        case 'help':
          socket.emit('terminal_output', {
            type: 'help',
            content: `Available Commands:
• analyze <dream> - Analyze your dream with AI
• samples - View sample dreams
• clear - Clear terminal
• status - System status
• 3d - Toggle 3D visualization

Example: analyze I was flying over a dark forest`,
            timestamp: new Date().toISOString()
          });
          break;
          
        case 'analyze':
          if (!args || args.trim().length < 10) {
            socket.emit('terminal_output', {
              type: 'error',
              content: 'Please provide a dream description (at least 10 characters)\nExample: analyze I was flying over a beautiful ocean',
              timestamp: new Date().toISOString()
            });
            break;
          }
          
          // Send processing message
          socket.emit('terminal_output', {
            type: 'processing',
            content: '🧠 AI is analyzing your dream...',
            timestamp: new Date().toISOString()
          });
          
          // Perform analysis
          const analysis = await dreamAnalyzer.analyzeDream(args, sessionId);
          session.dreamCount++;
          
          // Send analysis results
          socket.emit('dream_analysis', analysis);
          socket.emit('terminal_output', {
            type: 'success',
            content: `✅ Analysis complete! Confidence: ${analysis.confidence}%\n🎮 3D visualization ready - type '3d' to view`,
            timestamp: new Date().toISOString()
          });
          break;
          
        case 'samples':
          const samples = [
            "I was flying over a dark forest at night, feeling both scared and excited",
            "I found myself in my childhood home, but water was flowing through all the rooms",
            "I was driving a car but couldn't control it, going faster and faster",
            "I was swimming with dolphins in crystal clear blue water, feeling peaceful",
            "I was falling through clouds, spinning helplessly toward the ground"
          ];
          
          socket.emit('terminal_output', {
            type: 'info',
            content: `Sample Dreams:\n${samples.map((s, i) => `${i + 1}. ${s}`).join('\n')}\n\nTry: analyze ${samples[0]}`,
            timestamp: new Date().toISOString()
          });
          break;
          
        case 'clear':
          socket.emit('clear_terminal');
          break;
          
        case 'status':
          socket.emit('terminal_output', {
            type: 'info',
            content: `System Status:
• Active connections: ${io.engine.clientsCount}
• Your dreams analyzed: ${session.dreamCount}
• Session uptime: ${Math.round((Date.now() - session.connectedAt) / 1000)}s
• AI Engine: Online ✅
• 3D Renderer: Ready ✅`,
            timestamp: new Date().toISOString()
          });
          break;
          
        case '3d':
          socket.emit('toggle_3d');
          socket.emit('terminal_output', {
            type: 'info',
            content: '🎮 3D visualization toggled',
            timestamp: new Date().toISOString()
          });
          break;
          
        default:
          socket.emit('terminal_output', {
            type: 'error',
            content: `Unknown command: ${command}\nType 'help' for available commands`,
            timestamp: new Date().toISOString()
          });
      }
    } catch (error) {
      console.error('Command processing error:', error);
      socket.emit('terminal_output', {
        type: 'error',
        content: 'An error occurred processing your command. Please try again.',
        timestamp: new Date().toISOString()
      });
    }
  });
  
  socket.on('disconnect', () => {
    console.log(`🔌 Disconnected: ${socket.id}`);
    activeSessions.delete(sessionId);
  });
});

// Serve React app for any non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
server.listen(PORT, '0.0.0.0', () => {
  const os = require('os');
  const interfaces = os.networkInterfaces();
  let localIP = 'localhost';

  // Find the local IP address
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        localIP = interface.address;
        break;
      }
    }
  }

  console.log(`🚀 DreamScape AI Terminal Server running on:`);
  console.log(`   • Local:    http://localhost:${PORT}`);
  console.log(`   • Network:  http://${localIP}:${PORT}`);
  console.log(`📱 Mobile access: http://${localIP}:${PORT}`);
  console.log(`🌐 Network access enabled for mobile devices`);
  console.log(`🔥 Server listening on all interfaces (0.0.0.0:${PORT})`);
});

module.exports = { app, server, io };
