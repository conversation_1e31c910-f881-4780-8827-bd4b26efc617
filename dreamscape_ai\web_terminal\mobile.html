<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>🌙 DreamScape AI Mobile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #00ff41;
            height: 100vh;
            overflow: hidden;
            touch-action: manipulation;
        }
        
        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 10px;
        }
        
        .header {
            text-align: center;
            padding: 10px;
            background: rgba(0, 255, 65, 0.1);
            border-radius: 10px;
            margin-bottom: 10px;
        }
        
        .title {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .status {
            font-size: 0.8rem;
            margin-top: 5px;
        }
        
        .terminal {
            flex: 1;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .messages {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .message {
            margin-bottom: 8px;
            word-wrap: break-word;
        }
        
        .message.user { color: #66ff66; }
        .message.error { color: #ff4444; }
        .message.success { color: #00ff41; }
        .message.info { color: #0066cc; }
        .message.processing { color: #ffaa00; }
        
        .input-area {
            padding: 10px;
            border-top: 1px solid rgba(0, 255, 65, 0.3);
            display: flex;
            align-items: center;
        }
        
        .prompt {
            color: #00ff41;
            margin-right: 8px;
            font-weight: bold;
            font-size: 0.8rem;
        }
        
        .input {
            flex: 1;
            background: transparent;
            border: none;
            color: #00ff41;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            outline: none;
        }
        
        .input::placeholder {
            color: rgba(0, 255, 65, 0.5);
        }
        
        .send-btn {
            background: rgba(0, 255, 65, 0.2);
            border: 1px solid rgba(0, 255, 65, 0.5);
            color: #00ff41;
            padding: 8px 12px;
            border-radius: 5px;
            margin-left: 8px;
            font-size: 0.8rem;
            cursor: pointer;
            touch-action: manipulation;
        }
        
        .send-btn:active {
            background: rgba(0, 255, 65, 0.4);
        }
        
        .quick-commands {
            display: flex;
            gap: 5px;
            padding: 10px;
            overflow-x: auto;
        }
        
        .quick-cmd {
            background: rgba(0, 255, 65, 0.1);
            border: 1px solid rgba(0, 255, 65, 0.3);
            color: #00ff41;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            white-space: nowrap;
            cursor: pointer;
            touch-action: manipulation;
        }
        
        .quick-cmd:active {
            background: rgba(0, 255, 65, 0.3);
        }
        
        .connection-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            z-index: 1000;
        }
        
        .connection-indicator.connected {
            background: #00ff41;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .processing {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="connection-indicator" id="connectionIndicator"></div>
    
    <div class="container">
        <div class="header">
            <div class="title">🌙 DreamScape AI</div>
            <div class="status" id="status">Připojování...</div>
        </div>
        
        <div class="quick-commands">
            <div class="quick-cmd" onclick="sendCommand('help')">help</div>
            <div class="quick-cmd" onclick="sendCommand('samples')">samples</div>
            <div class="quick-cmd" onclick="sendCommand('status')">status</div>
            <div class="quick-cmd" onclick="sendCommand('3d')">3d</div>
            <div class="quick-cmd" onclick="sendCommand('clear')">clear</div>
        </div>
        
        <div class="terminal">
            <div class="messages" id="messages"></div>
            
            <form class="input-area" onsubmit="handleSubmit(event)">
                <span class="prompt">$</span>
                <input type="text" class="input" id="commandInput" 
                       placeholder="Napiš 'help' nebo 'analyze <sen>'" 
                       autocomplete="off" autocapitalize="off">
                <button type="submit" class="send-btn">Send</button>
            </form>
        </div>
    </div>
    
    <script src="/socket.io/socket.io.js"></script>
    <script>
        // Socket.io connection
        const socket = io({
            transports: ['websocket', 'polling'],
            upgrade: true,
            rememberUpgrade: true
        });
        
        const messages = document.getElementById('messages');
        const commandInput = document.getElementById('commandInput');
        const status = document.getElementById('status');
        const connectionIndicator = document.getElementById('connectionIndicator');
        
        let isProcessing = false;
        
        // Connection events
        socket.on('connect', () => {
            console.log('Connected to DreamScape AI');
            status.textContent = 'Připojeno ✅';
            connectionIndicator.classList.add('connected');
        });
        
        socket.on('disconnect', () => {
            console.log('Disconnected from DreamScape AI');
            status.textContent = 'Odpojeno ❌';
            connectionIndicator.classList.remove('connected');
        });
        
        // Terminal events
        socket.on('terminal_output', (message) => {
            addMessage(message.content, message.type);
            if (message.type === 'processing') {
                isProcessing = true;
            } else {
                isProcessing = false;
            }
        });
        
        socket.on('clear_terminal', () => {
            messages.innerHTML = '';
        });
        
        socket.on('dream_analysis', (analysis) => {
            console.log('Dream analysis received:', analysis);
            addMessage(`🧠 Analýza dokončena! Confidence: ${analysis.confidence}%`, 'success');
            addMessage(`🎮 Prostředí: ${analysis.environment}`, 'info');
            addMessage(`😊 Emoce: ${Object.keys(analysis.emotions).join(', ')}`, 'info');
            addMessage(`🔮 Symboly: ${analysis.symbols.map(s => s.name).join(', ')}`, 'info');
            isProcessing = false;
        });
        
        function addMessage(content, type = 'system') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = content;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }
        
        function sendCommand(command) {
            if (isProcessing) return;
            
            commandInput.value = command;
            handleSubmit(new Event('submit'));
        }
        
        function handleSubmit(event) {
            event.preventDefault();
            
            const command = commandInput.value.trim();
            if (!command || isProcessing) return;
            
            // Parse command
            const parts = command.split(' ');
            const cmd = parts[0];
            const args = parts.slice(1).join(' ');
            
            // Add user input to messages
            addMessage(`> ${command}`, 'user');
            
            // Set processing state for analyze command
            if (cmd.toLowerCase() === 'analyze' && args) {
                isProcessing = true;
                addMessage('🧠 Analyzuji sen...', 'processing');
            }
            
            // Send command to server
            socket.emit('command_input', { command: cmd, args });
            
            // Clear input
            commandInput.value = '';
        }
        
        // Auto-focus input
        commandInput.focus();
        
        // Prevent zoom on double tap
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
