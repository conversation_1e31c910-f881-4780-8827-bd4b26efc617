{"ast": null, "code": "import { useContext, useRef, useInsertionEffect, useEffect } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nfunction useVisualElement(Component, visualState, props, createVisualElement) {\n  const {\n    visualElement: parent\n  } = useContext(MotionContext);\n  const lazyContext = useContext(LazyContext);\n  const presenceContext = useContext(PresenceContext);\n  const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n  const visualElementRef = useRef();\n  /**\n   * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n   */\n  createVisualElement = createVisualElement || lazyContext.renderer;\n  if (!visualElementRef.current && createVisualElement) {\n    visualElementRef.current = createVisualElement(Component, {\n      visualState,\n      parent,\n      props,\n      presenceContext,\n      blockInitialAnimation: presenceContext ? presenceContext.initial === false : false,\n      reducedMotionConfig\n    });\n  }\n  const visualElement = visualElementRef.current;\n  useInsertionEffect(() => {\n    visualElement && visualElement.update(props, presenceContext);\n  });\n  /**\n   * Cache this value as we want to know whether HandoffAppearAnimations\n   * was present on initial render - it will be deleted after this.\n   */\n  const wantsHandoff = useRef(Boolean(props[optimizedAppearDataAttribute] && !window.HandoffComplete));\n  useIsomorphicLayoutEffect(() => {\n    if (!visualElement) return;\n    visualElement.render();\n    /**\n     * Ideally this function would always run in a useEffect.\n     *\n     * However, if we have optimised appear animations to handoff from,\n     * it needs to happen synchronously to ensure there's no flash of\n     * incorrect styles in the event of a hydration error.\n     *\n     * So if we detect a situtation where optimised appear animations\n     * are running, we use useLayoutEffect to trigger animations.\n     */\n    if (wantsHandoff.current && visualElement.animationState) {\n      visualElement.animationState.animateChanges();\n    }\n  });\n  useEffect(() => {\n    if (!visualElement) return;\n    visualElement.updateFeatures();\n    if (!wantsHandoff.current && visualElement.animationState) {\n      visualElement.animationState.animateChanges();\n    }\n    if (wantsHandoff.current) {\n      wantsHandoff.current = false;\n      // This ensures all future calls to animateChanges() will run in useEffect\n      window.HandoffComplete = true;\n    }\n  });\n  return visualElement;\n}\nexport { useVisualElement };", "map": {"version": 3, "names": ["useContext", "useRef", "useInsertionEffect", "useEffect", "PresenceContext", "MotionContext", "useIsomorphicLayoutEffect", "LazyContext", "MotionConfigContext", "optimizedAppearDataAttribute", "useVisualElement", "Component", "visualState", "props", "createVisualElement", "visualElement", "parent", "lazyContext", "presenceContext", "reducedMotionConfig", "reducedMotion", "visualElementRef", "renderer", "current", "blockInitialAnimation", "initial", "update", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "window", "HandoffComplete", "render", "animationState", "animateChanges", "updateFeatures"], "sources": ["C:/Users/<USER>/Desktop/lightr or dead/dreamscape_ai/web_terminal/client/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs"], "sourcesContent": ["import { useContext, useRef, useInsertionEffect, useEffect } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\n\nfunction useVisualElement(Component, visualState, props, createVisualElement) {\n    const { visualElement: parent } = useContext(MotionContext);\n    const lazyContext = useContext(LazyContext);\n    const presenceContext = useContext(PresenceContext);\n    const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n    const visualElementRef = useRef();\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement = createVisualElement || lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceContext,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    useInsertionEffect(() => {\n        visualElement && visualElement.update(props, presenceContext);\n    });\n    /**\n     * Cache this value as we want to know whether HandoffAppearAnimations\n     * was present on initial render - it will be deleted after this.\n     */\n    const wantsHandoff = useRef(Boolean(props[optimizedAppearDataAttribute] && !window.HandoffComplete));\n    useIsomorphicLayoutEffect(() => {\n        if (!visualElement)\n            return;\n        visualElement.render();\n        /**\n         * Ideally this function would always run in a useEffect.\n         *\n         * However, if we have optimised appear animations to handoff from,\n         * it needs to happen synchronously to ensure there's no flash of\n         * incorrect styles in the event of a hydration error.\n         *\n         * So if we detect a situtation where optimised appear animations\n         * are running, we use useLayoutEffect to trigger animations.\n         */\n        if (wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    useEffect(() => {\n        if (!visualElement)\n            return;\n        visualElement.updateFeatures();\n        if (!wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n        if (wantsHandoff.current) {\n            wantsHandoff.current = false;\n            // This ensures all future calls to animateChanges() will run in useEffect\n            window.HandoffComplete = true;\n        }\n    });\n    return visualElement;\n}\n\nexport { useVisualElement };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,OAAO;AACzE,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,4BAA4B,QAAQ,8CAA8C;AAE3F,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,mBAAmB,EAAE;EAC1E,MAAM;IAAEC,aAAa,EAAEC;EAAO,CAAC,GAAGhB,UAAU,CAACK,aAAa,CAAC;EAC3D,MAAMY,WAAW,GAAGjB,UAAU,CAACO,WAAW,CAAC;EAC3C,MAAMW,eAAe,GAAGlB,UAAU,CAACI,eAAe,CAAC;EACnD,MAAMe,mBAAmB,GAAGnB,UAAU,CAACQ,mBAAmB,CAAC,CAACY,aAAa;EACzE,MAAMC,gBAAgB,GAAGpB,MAAM,CAAC,CAAC;EACjC;AACJ;AACA;EACIa,mBAAmB,GAAGA,mBAAmB,IAAIG,WAAW,CAACK,QAAQ;EACjE,IAAI,CAACD,gBAAgB,CAACE,OAAO,IAAIT,mBAAmB,EAAE;IAClDO,gBAAgB,CAACE,OAAO,GAAGT,mBAAmB,CAACH,SAAS,EAAE;MACtDC,WAAW;MACXI,MAAM;MACNH,KAAK;MACLK,eAAe;MACfM,qBAAqB,EAAEN,eAAe,GAChCA,eAAe,CAACO,OAAO,KAAK,KAAK,GACjC,KAAK;MACXN;IACJ,CAAC,CAAC;EACN;EACA,MAAMJ,aAAa,GAAGM,gBAAgB,CAACE,OAAO;EAC9CrB,kBAAkB,CAAC,MAAM;IACrBa,aAAa,IAAIA,aAAa,CAACW,MAAM,CAACb,KAAK,EAAEK,eAAe,CAAC;EACjE,CAAC,CAAC;EACF;AACJ;AACA;AACA;EACI,MAAMS,YAAY,GAAG1B,MAAM,CAAC2B,OAAO,CAACf,KAAK,CAACJ,4BAA4B,CAAC,IAAI,CAACoB,MAAM,CAACC,eAAe,CAAC,CAAC;EACpGxB,yBAAyB,CAAC,MAAM;IAC5B,IAAI,CAACS,aAAa,EACd;IACJA,aAAa,CAACgB,MAAM,CAAC,CAAC;IACtB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIJ,YAAY,CAACJ,OAAO,IAAIR,aAAa,CAACiB,cAAc,EAAE;MACtDjB,aAAa,CAACiB,cAAc,CAACC,cAAc,CAAC,CAAC;IACjD;EACJ,CAAC,CAAC;EACF9B,SAAS,CAAC,MAAM;IACZ,IAAI,CAACY,aAAa,EACd;IACJA,aAAa,CAACmB,cAAc,CAAC,CAAC;IAC9B,IAAI,CAACP,YAAY,CAACJ,OAAO,IAAIR,aAAa,CAACiB,cAAc,EAAE;MACvDjB,aAAa,CAACiB,cAAc,CAACC,cAAc,CAAC,CAAC;IACjD;IACA,IAAIN,YAAY,CAACJ,OAAO,EAAE;MACtBI,YAAY,CAACJ,OAAO,GAAG,KAAK;MAC5B;MACAM,MAAM,CAACC,eAAe,GAAG,IAAI;IACjC;EACJ,CAAC,CAAC;EACF,OAAOf,aAAa;AACxB;AAEA,SAASL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}