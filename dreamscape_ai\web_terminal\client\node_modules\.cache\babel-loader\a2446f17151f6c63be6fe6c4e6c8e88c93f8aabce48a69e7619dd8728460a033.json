{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lightr or dead\\\\dreamscape_ai\\\\web_terminal\\\\client\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * DreamScape AI - Modern Terminal Web Application\n * React + TypeScript + Three.js + Socket.io\n */\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport styled, { createGlobalStyle, ThemeProvider } from 'styled-components';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { io } from 'socket.io-client';\n// Components will be created\n// import Terminal from './components/Terminal';\n// import Scene3D from './components/Scene3D';\n// import AnalysisPanel from './components/AnalysisPanel';\n// import MobileControls from './components/MobileControls';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Global styles\nconst GlobalStyle = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  body {\n    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;\n    background: #0a0a0a;\n    color: #00ff41;\n    overflow: hidden;\n    user-select: none;\n  }\n\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: rgba(0, 255, 65, 0.1);\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: rgba(0, 255, 65, 0.3);\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: rgba(0, 255, 65, 0.5);\n  }\n`;\n\n// Theme\n_c = GlobalStyle;\nconst theme = {\n  colors: {\n    primary: '#00ff41',\n    secondary: '#0066cc',\n    accent: '#ff6b35',\n    background: '#0a0a0a',\n    surface: '#1a1a1a',\n    text: '#00ff41',\n    textSecondary: '#66ff66',\n    error: '#ff4444',\n    warning: '#ffaa00',\n    success: '#00ff41'\n  },\n  fonts: {\n    mono: \"'JetBrains Mono', 'Fira Code', 'Consolas', monospace\",\n    sans: \"'Inter', 'Helvetica', sans-serif\"\n  },\n  breakpoints: {\n    mobile: '768px',\n    tablet: '1024px',\n    desktop: '1440px'\n  }\n};\n\n// Styled components\nconst AppContainer = styled.div`\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);\n  position: relative;\n  overflow: hidden;\n`;\n_c2 = AppContainer;\nconst Header = styled(motion.header)`\n  padding: 1rem 2rem;\n  background: rgba(0, 255, 65, 0.05);\n  border-bottom: 1px solid rgba(0, 255, 65, 0.2);\n  backdrop-filter: blur(10px);\n  z-index: 100;\n`;\n_c3 = Header;\nconst Title = styled.h1`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: ${props => props.theme.colors.primary};\n  text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);\n  \n  @media (max-width: ${props => props.theme.breakpoints.mobile}) {\n    font-size: 1.2rem;\n  }\n`;\n_c4 = Title;\nconst MainContent = styled.div`\n  flex: 1;\n  display: grid;\n  grid-template-columns: ${props => props.show3D ? '1fr 1fr' : '1fr'};\n  gap: 1rem;\n  padding: 1rem;\n  min-height: 0;\n  \n  @media (max-width: ${props => props.theme.breakpoints.mobile}) {\n    grid-template-columns: 1fr;\n    grid-template-rows: ${props => props.show3D ? '1fr 1fr' : '1fr'};\n    padding: 0.5rem;\n  }\n`;\n_c5 = MainContent;\nconst TerminalContainer = styled(motion.div)`\n  background: rgba(0, 0, 0, 0.8);\n  border: 1px solid rgba(0, 255, 65, 0.3);\n  border-radius: 8px;\n  backdrop-filter: blur(10px);\n  overflow: hidden;\n  position: relative;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 30px;\n    background: linear-gradient(90deg, #ff5f56, #ffbd2e, #27ca3f);\n    opacity: 0.8;\n  }\n`;\n_c6 = TerminalContainer;\nconst Scene3DContainer = styled(motion.div)`\n  background: rgba(0, 0, 0, 0.9);\n  border: 1px solid rgba(0, 255, 65, 0.3);\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n`;\n_c7 = Scene3DContainer;\nconst StatusBar = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 30px;\n  background: rgba(0, 255, 65, 0.1);\n  display: flex;\n  align-items: center;\n  padding: 0 1rem;\n  font-size: 0.8rem;\n  border-top: 1px solid rgba(0, 255, 65, 0.2);\n  z-index: 100;\n`;\n_c8 = StatusBar;\nconst ConnectionStatus = styled.span`\n  color: ${props => props.connected ? props.theme.colors.success : props.theme.colors.error};\n  margin-right: 1rem;\n  \n  &::before {\n    content: '●';\n    margin-right: 0.5rem;\n  }\n`;\n_c9 = ConnectionStatus;\nconst FloatingParticles = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 1;\n`;\n\n// Terminal styles\n_c0 = FloatingParticles;\nconst TerminalContent = styled.div`\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  padding-top: 30px;\n`;\n_c1 = TerminalContent;\nconst MessagesContainer = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding: 1rem;\n  font-family: ${props => props.theme.fonts.mono};\n  font-size: 0.9rem;\n  line-height: 1.4;\n`;\n_c10 = MessagesContainer;\nconst Message = styled.div`\n  margin-bottom: 0.5rem;\n  color: ${props => {\n  switch (props.type) {\n    case 'error':\n      return props.theme.colors.error;\n    case 'success':\n      return props.theme.colors.success;\n    case 'warning':\n      return props.theme.colors.warning;\n    case 'info':\n      return props.theme.colors.secondary;\n    case 'processing':\n      return props.theme.colors.accent;\n    case 'user':\n      return props.theme.colors.textSecondary;\n    default:\n      return props.theme.colors.text;\n  }\n}};\n\n  white-space: pre-wrap;\n  word-wrap: break-word;\n`;\n_c11 = Message;\nconst InputContainer = styled.div`\n  padding: 1rem;\n  border-top: 1px solid rgba(0, 255, 65, 0.2);\n  display: flex;\n  align-items: center;\n`;\n_c12 = InputContainer;\nconst Prompt = styled.span`\n  color: ${props => props.theme.colors.primary};\n  margin-right: 0.5rem;\n  font-weight: bold;\n`;\n_c13 = Prompt;\nconst Input = styled.input`\n  flex: 1;\n  background: transparent;\n  border: none;\n  color: ${props => props.theme.colors.text};\n  font-family: ${props => props.theme.fonts.mono};\n  font-size: 0.9rem;\n  outline: none;\n\n  &::placeholder {\n    color: rgba(0, 255, 65, 0.5);\n  }\n`;\n\n// Simple Terminal Component\n_c14 = Input;\nconst SimpleTerminal = ({\n  messages,\n  onCommand,\n  isAnalyzing,\n  connected\n}) => {\n  _s();\n  const [input, setInput] = useState('');\n  const messagesEndRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages]);\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (input.trim() && connected && !isAnalyzing) {\n      onCommand(input.trim());\n      setInput('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(TerminalContent, {\n    children: [/*#__PURE__*/_jsxDEV(MessagesContainer, {\n      children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(Message, {\n        type: message.type,\n        children: message.content\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this)), isAnalyzing && /*#__PURE__*/_jsxDEV(Message, {\n        type: \"processing\",\n        children: \"\\uD83E\\uDDE0 Analyzing dream... Please wait...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: /*#__PURE__*/_jsxDEV(InputContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Prompt, {\n          children: \"dreamscape@ai:~$\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          type: \"text\",\n          value: input,\n          onChange: e => setInput(e.target.value),\n          placeholder: connected ? \"Type 'help' for commands or 'analyze <dream>'\" : \"Connecting...\",\n          disabled: !connected || isAnalyzing\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 317,\n    columnNumber: 5\n  }, this);\n};\n\n// Simple 3D Scene Component\n_s(SimpleTerminal, \"X/z4qmiNhorkSBII6TQsWlgEcpQ=\");\n_c15 = SimpleTerminal;\nconst Scene3DContent = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #001122 0%, #003366 100%);\n  position: relative;\n  overflow: hidden;\n`;\n_c16 = Scene3DContent;\nconst SceneInfo = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.7);\n  padding: 1rem;\n  border-radius: 8px;\n  border: 1px solid rgba(0, 255, 65, 0.3);\n  font-family: ${props => props.theme.fonts.mono};\n  font-size: 0.8rem;\n  z-index: 10;\n`;\n_c17 = SceneInfo;\nconst Simple3DScene = ({\n  analysis\n}) => {\n  if (!analysis) {\n    return /*#__PURE__*/_jsxDEV(Scene3DContent, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          color: '#666'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83C\\uDFAE 3D Dream Visualization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Analyze a dream to see 3D visualization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Scene3DContent, {\n    children: [/*#__PURE__*/_jsxDEV(SceneInfo, {\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: [\"\\uD83C\\uDF19 Dream: \", analysis.originalText.substring(0, 50), \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Environment:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 12\n        }, this), \" \", analysis.environment]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Emotions:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 12\n        }, this), \" \", Object.keys(analysis.emotions).join(', ')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Symbols:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 12\n        }, this), \" \", analysis.symbols.map(s => s.name).join(', ')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Confidence:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 12\n        }, this), \" \", analysis.confidence, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this), [...Array(10)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n      style: {\n        position: 'absolute',\n        width: '4px',\n        height: '4px',\n        background: analysis.scene3D.colorPalette[0] || '#00ff41',\n        borderRadius: '50%',\n        opacity: 0.6\n      },\n      animate: {\n        x: [0, Math.random() * 400 - 200],\n        y: [0, Math.random() * 400 - 200],\n        scale: [0, 1, 0]\n      },\n      transition: {\n        duration: 3 + Math.random() * 2,\n        repeat: Infinity,\n        ease: 'easeInOut'\n      }\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginTop: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83C\\uDFAE 3D Visualization Active\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Environment: \", analysis.scene3D.environment]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Elements: \", analysis.scene3D.elements3D.slice(0, 3).join(', ')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 388,\n    columnNumber: 5\n  }, this);\n};\n\n// Main App Component\n_c18 = Simple3DScene;\nconst App = () => {\n  _s2();\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const [messages, setMessages] = useState([]);\n  const [currentAnalysis, setCurrentAnalysis] = useState(null);\n  const [show3D, setShow3D] = useState(false);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const socketRef = useRef(null);\n\n  // Initialize socket connection\n  useEffect(() => {\n    const newSocket = io(process.env.NODE_ENV === 'production' ? window.location.origin : 'http://localhost:3001', {\n      transports: ['websocket', 'polling']\n    });\n    socketRef.current = newSocket;\n    setSocket(newSocket);\n\n    // Connection events\n    newSocket.on('connect', () => {\n      console.log('🔌 Connected to DreamScape AI');\n      setConnected(true);\n    });\n    newSocket.on('disconnect', () => {\n      console.log('🔌 Disconnected from DreamScape AI');\n      setConnected(false);\n    });\n\n    // Terminal events\n    newSocket.on('terminal_output', message => {\n      setMessages(prev => [...prev, message]);\n    });\n    newSocket.on('clear_terminal', () => {\n      setMessages([]);\n    });\n    newSocket.on('toggle_3d', () => {\n      setShow3D(prev => !prev);\n    });\n\n    // Dream analysis events\n    newSocket.on('dream_analysis', analysis => {\n      console.log('🧠 Received dream analysis:', analysis);\n      setCurrentAnalysis(analysis);\n      setIsAnalyzing(false);\n      setShow3D(true);\n    });\n    return () => {\n      newSocket.close();\n    };\n  }, []);\n\n  // Check if mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Handle command input\n  const handleCommand = input => {\n    if (!socket || !connected) {\n      setMessages(prev => [...prev, {\n        type: 'error',\n        content: 'Not connected to server. Please refresh the page.',\n        timestamp: new Date().toISOString()\n      }]);\n      return;\n    }\n\n    // Parse command\n    const parts = input.trim().split(' ');\n    const command = parts[0];\n    const args = parts.slice(1).join(' ');\n\n    // Add user input to messages\n    setMessages(prev => [...prev, {\n      type: 'user',\n      content: `> ${input}`,\n      timestamp: new Date().toISOString()\n    }]);\n\n    // Set analyzing state for analyze command\n    if (command.toLowerCase() === 'analyze' && args) {\n      setIsAnalyzing(true);\n    }\n\n    // Send command to server\n    socket.emit('command_input', {\n      command,\n      args\n    });\n  };\n\n  // Handle 3D scene interaction\n  const handle3DInteraction = (type, data) => {\n    console.log('3D Interaction:', type, data);\n    if (type === 'object_click' && data) {\n      setMessages(prev => [...prev, {\n        type: 'info',\n        content: `🎮 Clicked on ${data.object} in 3D scene`,\n        timestamp: new Date().toISOString()\n      }]);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(GlobalStyle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppContainer, {\n      children: [/*#__PURE__*/_jsxDEV(FloatingParticles, {\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          style: {\n            position: 'absolute',\n            width: '2px',\n            height: '2px',\n            background: theme.colors.primary,\n            borderRadius: '50%',\n            opacity: 0.3\n          },\n          animate: {\n            x: [0, Math.random() * window.innerWidth],\n            y: [0, Math.random() * window.innerHeight],\n            opacity: [0, 0.6, 0]\n          },\n          transition: {\n            duration: 10 + Math.random() * 10,\n            repeat: Infinity,\n            ease: 'linear'\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Header, {\n        initial: {\n          y: -50,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(Title, {\n          children: \"\\uD83C\\uDF19 DreamScape AI Terminal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        show3D: show3D,\n        children: [/*#__PURE__*/_jsxDEV(TerminalContainer, {\n          initial: {\n            x: -100,\n            opacity: 0\n          },\n          animate: {\n            x: 0,\n            opacity: 1\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.2\n          },\n          children: /*#__PURE__*/_jsxDEV(SimpleTerminal, {\n            messages: messages,\n            onCommand: handleCommand,\n            isAnalyzing: isAnalyzing,\n            connected: connected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: show3D && /*#__PURE__*/_jsxDEV(Scene3DContainer, {\n            initial: {\n              x: 100,\n              opacity: 0,\n              scale: 0.8\n            },\n            animate: {\n              x: 0,\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              x: 100,\n              opacity: 0,\n              scale: 0.8\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: /*#__PURE__*/_jsxDEV(Simple3DScene, {\n              analysis: currentAnalysis\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this), isMobile && currentAnalysis && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          bottom: '60px',\n          left: '10px',\n          right: '10px',\n          background: 'rgba(0, 0, 0, 0.9)',\n          padding: '10px',\n          borderRadius: '8px',\n          border: '1px solid rgba(0, 255, 65, 0.3)',\n          fontSize: '0.8rem',\n          zIndex: 1000\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\uD83C\\uDF19 \", currentAnalysis.originalText.substring(0, 30), \"...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Confidence: \", currentAnalysis.confidence, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(StatusBar, {\n        children: [/*#__PURE__*/_jsxDEV(ConnectionStatus, {\n          connected: connected,\n          children: connected ? 'Connected' : 'Disconnected'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Messages: \", messages.length, \" | 3D: \", show3D ? 'ON' : 'OFF', \" | Analysis: \", currentAnalysis ? 'Ready' : 'None']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 547,\n    columnNumber: 5\n  }, this);\n};\n_s2(App, \"W7Izh0BzQyUSCfMHTmtDUxQNPYE=\");\n_c19 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19;\n$RefreshReg$(_c, \"GlobalStyle\");\n$RefreshReg$(_c2, \"AppContainer\");\n$RefreshReg$(_c3, \"Header\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"MainContent\");\n$RefreshReg$(_c6, \"TerminalContainer\");\n$RefreshReg$(_c7, \"Scene3DContainer\");\n$RefreshReg$(_c8, \"StatusBar\");\n$RefreshReg$(_c9, \"ConnectionStatus\");\n$RefreshReg$(_c0, \"FloatingParticles\");\n$RefreshReg$(_c1, \"TerminalContent\");\n$RefreshReg$(_c10, \"MessagesContainer\");\n$RefreshReg$(_c11, \"Message\");\n$RefreshReg$(_c12, \"InputContainer\");\n$RefreshReg$(_c13, \"Prompt\");\n$RefreshReg$(_c14, \"Input\");\n$RefreshReg$(_c15, \"SimpleTerminal\");\n$RefreshReg$(_c16, \"Scene3DContent\");\n$RefreshReg$(_c17, \"SceneInfo\");\n$RefreshReg$(_c18, \"Simple3DScene\");\n$RefreshReg$(_c19, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "styled", "createGlobalStyle", "ThemeProvider", "motion", "AnimatePresence", "io", "jsxDEV", "_jsxDEV", "GlobalStyle", "_c", "theme", "colors", "primary", "secondary", "accent", "background", "surface", "text", "textSecondary", "error", "warning", "success", "fonts", "mono", "sans", "breakpoints", "mobile", "tablet", "desktop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c2", "Header", "header", "_c3", "Title", "h1", "props", "_c4", "MainContent", "show3D", "_c5", "TerminalContainer", "_c6", "Scene3DC<PERSON><PERSON>", "_c7", "StatusBar", "_c8", "ConnectionStatus", "span", "connected", "_c9", "FloatingParticles", "_c0", "TerminalContent", "_c1", "MessagesContainer", "_c10", "Message", "type", "_c11", "InputContainer", "_c12", "Prompt", "_c13", "Input", "input", "_c14", "SimpleTerminal", "messages", "onCommand", "isAnalyzing", "_s", "setInput", "messagesEndRef", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSubmit", "e", "preventDefault", "trim", "children", "map", "message", "index", "content", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onSubmit", "value", "onChange", "target", "placeholder", "disabled", "_c15", "Scene3DContent", "_c16", "SceneInfo", "_c17", "Simple3DScene", "analysis", "style", "textAlign", "color", "originalText", "substring", "environment", "Object", "keys", "emotions", "join", "symbols", "s", "name", "confidence", "Array", "_", "i", "position", "width", "height", "scene3D", "colorPalette", "borderRadius", "opacity", "animate", "x", "Math", "random", "y", "scale", "transition", "duration", "repeat", "Infinity", "ease", "marginTop", "elements3D", "slice", "_c18", "App", "_s2", "socket", "setSocket", "setConnected", "setMessages", "currentAnalysis", "setCurrentAnalysis", "setShow3D", "setIsAnalyzing", "isMobile", "setIsMobile", "socketRef", "newSocket", "process", "env", "NODE_ENV", "window", "location", "origin", "transports", "on", "console", "log", "prev", "close", "checkMobile", "innerWidth", "addEventListener", "removeEventListener", "handleCommand", "timestamp", "Date", "toISOString", "parts", "split", "command", "args", "toLowerCase", "emit", "handle3DInteraction", "data", "object", "innerHeight", "initial", "delay", "exit", "bottom", "left", "right", "padding", "border", "fontSize", "zIndex", "length", "_c19", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/lightr or dead/dreamscape_ai/web_terminal/client/src/App.tsx"], "sourcesContent": ["/**\n * DreamScape AI - Modern Terminal Web Application\n * React + TypeScript + Three.js + Socket.io\n */\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport styled, { createGlobalStyle, ThemeProvider } from 'styled-components';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { io, Socket } from 'socket.io-client';\n// Components will be created\n// import Terminal from './components/Terminal';\n// import Scene3D from './components/Scene3D';\n// import AnalysisPanel from './components/AnalysisPanel';\n// import MobileControls from './components/MobileControls';\n\n// Types\ninterface TerminalMessage {\n  type: 'system' | 'user' | 'error' | 'success' | 'info' | 'processing' | 'help';\n  content: string;\n  timestamp: string;\n}\n\ninterface DreamAnalysis {\n  dreamId: string;\n  sessionId: string;\n  timestamp: string;\n  originalText: string;\n  emotions: Record<string, number>;\n  symbols: Array<{\n    name: string;\n    meanings: string[];\n    colors: string[];\n    elements3D: string[];\n    importance: number;\n  }>;\n  environment: string;\n  scene3D: {\n    environment: string;\n    colorPalette: string[];\n    lighting: any;\n    elements3D: string[];\n    atmosphere: any;\n    camera: any;\n    audio: any;\n  };\n  insights: {\n    emotional: string;\n    symbolic: string;\n    psychological: string;\n    recommendation: string;\n  };\n  confidence: number;\n  processingTime: number;\n  ready: boolean;\n}\n\n// Global styles\nconst GlobalStyle = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  body {\n    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;\n    background: #0a0a0a;\n    color: #00ff41;\n    overflow: hidden;\n    user-select: none;\n  }\n\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: rgba(0, 255, 65, 0.1);\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: rgba(0, 255, 65, 0.3);\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: rgba(0, 255, 65, 0.5);\n  }\n`;\n\n// Theme\nconst theme = {\n  colors: {\n    primary: '#00ff41',\n    secondary: '#0066cc',\n    accent: '#ff6b35',\n    background: '#0a0a0a',\n    surface: '#1a1a1a',\n    text: '#00ff41',\n    textSecondary: '#66ff66',\n    error: '#ff4444',\n    warning: '#ffaa00',\n    success: '#00ff41'\n  },\n  fonts: {\n    mono: \"'JetBrains Mono', 'Fira Code', 'Consolas', monospace\",\n    sans: \"'Inter', 'Helvetica', sans-serif\"\n  },\n  breakpoints: {\n    mobile: '768px',\n    tablet: '1024px',\n    desktop: '1440px'\n  }\n};\n\n// Styled components\nconst AppContainer = styled.div`\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);\n  position: relative;\n  overflow: hidden;\n`;\n\nconst Header = styled(motion.header)`\n  padding: 1rem 2rem;\n  background: rgba(0, 255, 65, 0.05);\n  border-bottom: 1px solid rgba(0, 255, 65, 0.2);\n  backdrop-filter: blur(10px);\n  z-index: 100;\n`;\n\nconst Title = styled.h1`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: ${props => props.theme.colors.primary};\n  text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);\n  \n  @media (max-width: ${props => props.theme.breakpoints.mobile}) {\n    font-size: 1.2rem;\n  }\n`;\n\nconst MainContent = styled.div<{ show3D: boolean }>`\n  flex: 1;\n  display: grid;\n  grid-template-columns: ${props => props.show3D ? '1fr 1fr' : '1fr'};\n  gap: 1rem;\n  padding: 1rem;\n  min-height: 0;\n  \n  @media (max-width: ${props => props.theme.breakpoints.mobile}) {\n    grid-template-columns: 1fr;\n    grid-template-rows: ${props => props.show3D ? '1fr 1fr' : '1fr'};\n    padding: 0.5rem;\n  }\n`;\n\nconst TerminalContainer = styled(motion.div)`\n  background: rgba(0, 0, 0, 0.8);\n  border: 1px solid rgba(0, 255, 65, 0.3);\n  border-radius: 8px;\n  backdrop-filter: blur(10px);\n  overflow: hidden;\n  position: relative;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 30px;\n    background: linear-gradient(90deg, #ff5f56, #ffbd2e, #27ca3f);\n    opacity: 0.8;\n  }\n`;\n\nconst Scene3DContainer = styled(motion.div)`\n  background: rgba(0, 0, 0, 0.9);\n  border: 1px solid rgba(0, 255, 65, 0.3);\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n`;\n\nconst StatusBar = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 30px;\n  background: rgba(0, 255, 65, 0.1);\n  display: flex;\n  align-items: center;\n  padding: 0 1rem;\n  font-size: 0.8rem;\n  border-top: 1px solid rgba(0, 255, 65, 0.2);\n  z-index: 100;\n`;\n\nconst ConnectionStatus = styled.span<{ connected: boolean }>`\n  color: ${props => props.connected ? props.theme.colors.success : props.theme.colors.error};\n  margin-right: 1rem;\n  \n  &::before {\n    content: '●';\n    margin-right: 0.5rem;\n  }\n`;\n\nconst FloatingParticles = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 1;\n`;\n\n// Terminal styles\nconst TerminalContent = styled.div`\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  padding-top: 30px;\n`;\n\nconst MessagesContainer = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding: 1rem;\n  font-family: ${props => props.theme.fonts.mono};\n  font-size: 0.9rem;\n  line-height: 1.4;\n`;\n\nconst Message = styled.div<{ type: string }>`\n  margin-bottom: 0.5rem;\n  color: ${props => {\n    switch (props.type) {\n      case 'error': return props.theme.colors.error;\n      case 'success': return props.theme.colors.success;\n      case 'warning': return props.theme.colors.warning;\n      case 'info': return props.theme.colors.secondary;\n      case 'processing': return props.theme.colors.accent;\n      case 'user': return props.theme.colors.textSecondary;\n      default: return props.theme.colors.text;\n    }\n  }};\n\n  white-space: pre-wrap;\n  word-wrap: break-word;\n`;\n\nconst InputContainer = styled.div`\n  padding: 1rem;\n  border-top: 1px solid rgba(0, 255, 65, 0.2);\n  display: flex;\n  align-items: center;\n`;\n\nconst Prompt = styled.span`\n  color: ${props => props.theme.colors.primary};\n  margin-right: 0.5rem;\n  font-weight: bold;\n`;\n\nconst Input = styled.input`\n  flex: 1;\n  background: transparent;\n  border: none;\n  color: ${props => props.theme.colors.text};\n  font-family: ${props => props.theme.fonts.mono};\n  font-size: 0.9rem;\n  outline: none;\n\n  &::placeholder {\n    color: rgba(0, 255, 65, 0.5);\n  }\n`;\n\n// Simple Terminal Component\ninterface SimpleTerminalProps {\n  messages: TerminalMessage[];\n  onCommand: (command: string) => void;\n  isAnalyzing: boolean;\n  connected: boolean;\n}\n\nconst SimpleTerminal: React.FC<SimpleTerminalProps> = ({\n  messages,\n  onCommand,\n  isAnalyzing,\n  connected\n}) => {\n  const [input, setInput] = useState('');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (input.trim() && connected && !isAnalyzing) {\n      onCommand(input.trim());\n      setInput('');\n    }\n  };\n\n  return (\n    <TerminalContent>\n      <MessagesContainer>\n        {messages.map((message, index) => (\n          <Message key={index} type={message.type}>\n            {message.content}\n          </Message>\n        ))}\n        {isAnalyzing && (\n          <Message type=\"processing\">\n            🧠 Analyzing dream... Please wait...\n          </Message>\n        )}\n        <div ref={messagesEndRef} />\n      </MessagesContainer>\n\n      <form onSubmit={handleSubmit}>\n        <InputContainer>\n          <Prompt>dreamscape@ai:~$</Prompt>\n          <Input\n            type=\"text\"\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            placeholder={connected ? \"Type 'help' for commands or 'analyze <dream>'\" : \"Connecting...\"}\n            disabled={!connected || isAnalyzing}\n          />\n        </InputContainer>\n      </form>\n    </TerminalContent>\n  );\n};\n\n// Simple 3D Scene Component\nconst Scene3DContent = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #001122 0%, #003366 100%);\n  position: relative;\n  overflow: hidden;\n`;\n\nconst SceneInfo = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.7);\n  padding: 1rem;\n  border-radius: 8px;\n  border: 1px solid rgba(0, 255, 65, 0.3);\n  font-family: ${props => props.theme.fonts.mono};\n  font-size: 0.8rem;\n  z-index: 10;\n`;\n\nconst Simple3DScene: React.FC<{ analysis: DreamAnalysis | null }> = ({ analysis }) => {\n  if (!analysis) {\n    return (\n      <Scene3DContent>\n        <div style={{ textAlign: 'center', color: '#666' }}>\n          <h3>🎮 3D Dream Visualization</h3>\n          <p>Analyze a dream to see 3D visualization</p>\n        </div>\n      </Scene3DContent>\n    );\n  }\n\n  return (\n    <Scene3DContent>\n      <SceneInfo>\n        <h4>🌙 Dream: {analysis.originalText.substring(0, 50)}...</h4>\n        <p><strong>Environment:</strong> {analysis.environment}</p>\n        <p><strong>Emotions:</strong> {Object.keys(analysis.emotions).join(', ')}</p>\n        <p><strong>Symbols:</strong> {analysis.symbols.map(s => s.name).join(', ')}</p>\n        <p><strong>Confidence:</strong> {analysis.confidence}%</p>\n      </SceneInfo>\n\n      {/* Animated background */}\n      {[...Array(10)].map((_, i) => (\n        <motion.div\n          key={i}\n          style={{\n            position: 'absolute',\n            width: '4px',\n            height: '4px',\n            background: analysis.scene3D.colorPalette[0] || '#00ff41',\n            borderRadius: '50%',\n            opacity: 0.6,\n          }}\n          animate={{\n            x: [0, Math.random() * 400 - 200],\n            y: [0, Math.random() * 400 - 200],\n            scale: [0, 1, 0],\n          }}\n          transition={{\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            ease: 'easeInOut',\n          }}\n        />\n      ))}\n\n      <div style={{ textAlign: 'center', marginTop: '2rem' }}>\n        <h3>🎮 3D Visualization Active</h3>\n        <p>Environment: {analysis.scene3D.environment}</p>\n        <p>Elements: {analysis.scene3D.elements3D.slice(0, 3).join(', ')}</p>\n      </div>\n    </Scene3DContent>\n  );\n};\n\n// Main App Component\nconst App: React.FC = () => {\n  const [socket, setSocket] = useState<Socket | null>(null);\n  const [connected, setConnected] = useState(false);\n  const [messages, setMessages] = useState<TerminalMessage[]>([]);\n  const [currentAnalysis, setCurrentAnalysis] = useState<DreamAnalysis | null>(null);\n  const [show3D, setShow3D] = useState(false);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  \n  const socketRef = useRef<Socket | null>(null);\n\n  // Initialize socket connection\n  useEffect(() => {\n    const newSocket = io(process.env.NODE_ENV === 'production' ? window.location.origin : 'http://localhost:3001', {\n      transports: ['websocket', 'polling']\n    });\n    \n    socketRef.current = newSocket;\n    setSocket(newSocket);\n\n    // Connection events\n    newSocket.on('connect', () => {\n      console.log('🔌 Connected to DreamScape AI');\n      setConnected(true);\n    });\n\n    newSocket.on('disconnect', () => {\n      console.log('🔌 Disconnected from DreamScape AI');\n      setConnected(false);\n    });\n\n    // Terminal events\n    newSocket.on('terminal_output', (message: TerminalMessage) => {\n      setMessages(prev => [...prev, message]);\n    });\n\n    newSocket.on('clear_terminal', () => {\n      setMessages([]);\n    });\n\n    newSocket.on('toggle_3d', () => {\n      setShow3D(prev => !prev);\n    });\n\n    // Dream analysis events\n    newSocket.on('dream_analysis', (analysis: DreamAnalysis) => {\n      console.log('🧠 Received dream analysis:', analysis);\n      setCurrentAnalysis(analysis);\n      setIsAnalyzing(false);\n      setShow3D(true);\n    });\n\n    return () => {\n      newSocket.close();\n    };\n  }, []);\n\n  // Check if mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    \n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    \n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Handle command input\n  const handleCommand = (input: string) => {\n    if (!socket || !connected) {\n      setMessages(prev => [...prev, {\n        type: 'error',\n        content: 'Not connected to server. Please refresh the page.',\n        timestamp: new Date().toISOString()\n      }]);\n      return;\n    }\n\n    // Parse command\n    const parts = input.trim().split(' ');\n    const command = parts[0];\n    const args = parts.slice(1).join(' ');\n\n    // Add user input to messages\n    setMessages(prev => [...prev, {\n      type: 'user',\n      content: `> ${input}`,\n      timestamp: new Date().toISOString()\n    }]);\n\n    // Set analyzing state for analyze command\n    if (command.toLowerCase() === 'analyze' && args) {\n      setIsAnalyzing(true);\n    }\n\n    // Send command to server\n    socket.emit('command_input', { command, args });\n  };\n\n  // Handle 3D scene interaction\n  const handle3DInteraction = (type: string, data?: any) => {\n    console.log('3D Interaction:', type, data);\n    \n    if (type === 'object_click' && data) {\n      setMessages(prev => [...prev, {\n        type: 'info',\n        content: `🎮 Clicked on ${data.object} in 3D scene`,\n        timestamp: new Date().toISOString()\n      }]);\n    }\n  };\n\n  return (\n    <ThemeProvider theme={theme}>\n      <GlobalStyle />\n      <AppContainer>\n        {/* Floating particles background */}\n        <FloatingParticles>\n          {[...Array(20)].map((_, i) => (\n            <motion.div\n              key={i}\n              style={{\n                position: 'absolute',\n                width: '2px',\n                height: '2px',\n                background: theme.colors.primary,\n                borderRadius: '50%',\n                opacity: 0.3,\n              }}\n              animate={{\n                x: [0, Math.random() * window.innerWidth],\n                y: [0, Math.random() * window.innerHeight],\n                opacity: [0, 0.6, 0],\n              }}\n              transition={{\n                duration: 10 + Math.random() * 10,\n                repeat: Infinity,\n                ease: 'linear',\n              }}\n            />\n          ))}\n        </FloatingParticles>\n\n        {/* Header */}\n        <Header\n          initial={{ y: -50, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ duration: 0.5 }}\n        >\n          <Title>🌙 DreamScape AI Terminal</Title>\n        </Header>\n\n        {/* Main Content */}\n        <MainContent show3D={show3D}>\n          {/* Terminal */}\n          <TerminalContainer\n            initial={{ x: -100, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            <SimpleTerminal\n              messages={messages}\n              onCommand={handleCommand}\n              isAnalyzing={isAnalyzing}\n              connected={connected}\n            />\n          </TerminalContainer>\n\n          {/* 3D Scene */}\n          <AnimatePresence>\n            {show3D && (\n              <Scene3DContainer\n                initial={{ x: 100, opacity: 0, scale: 0.8 }}\n                animate={{ x: 0, opacity: 1, scale: 1 }}\n                exit={{ x: 100, opacity: 0, scale: 0.8 }}\n                transition={{ duration: 0.5 }}\n              >\n                <Simple3DScene analysis={currentAnalysis} />\n              </Scene3DContainer>\n            )}\n          </AnimatePresence>\n        </MainContent>\n\n        {/* Mobile info */}\n        {isMobile && currentAnalysis && (\n          <div style={{\n            position: 'fixed',\n            bottom: '60px',\n            left: '10px',\n            right: '10px',\n            background: 'rgba(0, 0, 0, 0.9)',\n            padding: '10px',\n            borderRadius: '8px',\n            border: '1px solid rgba(0, 255, 65, 0.3)',\n            fontSize: '0.8rem',\n            zIndex: 1000\n          }}>\n            <div>🌙 {currentAnalysis.originalText.substring(0, 30)}...</div>\n            <div>Confidence: {currentAnalysis.confidence}%</div>\n          </div>\n        )}\n\n        {/* Status Bar */}\n        <StatusBar>\n          <ConnectionStatus connected={connected}>\n            {connected ? 'Connected' : 'Disconnected'}\n          </ConnectionStatus>\n          <span>\n            Messages: {messages.length} | \n            3D: {show3D ? 'ON' : 'OFF'} | \n            Analysis: {currentAnalysis ? 'Ready' : 'None'}\n          </span>\n        </StatusBar>\n      </AppContainer>\n    </ThemeProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,MAAM,IAAIC,iBAAiB,EAAEC,aAAa,QAAQ,mBAAmB;AAC5E,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,EAAE,QAAgB,kBAAkB;AAC7C;AACA;AACA;AACA;AACA;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAyCA;AACA,MAAMC,WAAW,GAAGP,iBAAiB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAQ,EAAA,GAjCMD,WAAW;AAkCjB,MAAME,KAAK,GAAG;EACZC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,SAAS;IACxBC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAE;IACLC,IAAI,EAAE,sDAAsD;IAC5DC,IAAI,EAAE;EACR,CAAC;EACDC,WAAW,EAAE;IACXC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG7B,MAAM,CAAC8B,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GARIF,YAAY;AAUlB,MAAMG,MAAM,GAAGhC,MAAM,CAACG,MAAM,CAAC8B,MAAM,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,MAAM;AAQZ,MAAMG,KAAK,GAAGnC,MAAM,CAACoC,EAAE;AACvB;AACA;AACA,WAAWC,KAAK,IAAIA,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACC,OAAO;AAC9C;AACA;AACA,uBAAuByB,KAAK,IAAIA,KAAK,CAAC3B,KAAK,CAACe,WAAW,CAACC,MAAM;AAC9D;AACA;AACA,CAAC;AAACY,GAAA,GATIH,KAAK;AAWX,MAAMI,WAAW,GAAGvC,MAAM,CAAC8B,GAAwB;AACnD;AACA;AACA,2BAA2BO,KAAK,IAAIA,KAAK,CAACG,MAAM,GAAG,SAAS,GAAG,KAAK;AACpE;AACA;AACA;AACA;AACA,uBAAuBH,KAAK,IAAIA,KAAK,CAAC3B,KAAK,CAACe,WAAW,CAACC,MAAM;AAC9D;AACA,0BAA0BW,KAAK,IAAIA,KAAK,CAACG,MAAM,GAAG,SAAS,GAAG,KAAK;AACnE;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,WAAW;AAejB,MAAMG,iBAAiB,GAAG1C,MAAM,CAACG,MAAM,CAAC2B,GAAG,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAlBID,iBAAiB;AAoBvB,MAAME,gBAAgB,GAAG5C,MAAM,CAACG,MAAM,CAAC2B,GAAG,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GANID,gBAAgB;AAQtB,MAAME,SAAS,GAAG9C,MAAM,CAAC8B,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAbID,SAAS;AAef,MAAME,gBAAgB,GAAGhD,MAAM,CAACiD,IAA4B;AAC5D,WAAWZ,KAAK,IAAIA,KAAK,CAACa,SAAS,GAAGb,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACU,OAAO,GAAGgB,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACQ,KAAK;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,GAAA,GARIH,gBAAgB;AAUtB,MAAMI,iBAAiB,GAAGpD,MAAM,CAAC8B,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAuB,GAAA,GAVMD,iBAAiB;AAWvB,MAAME,eAAe,GAAGtD,MAAM,CAAC8B,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GALID,eAAe;AAOrB,MAAME,iBAAiB,GAAGxD,MAAM,CAAC8B,GAAG;AACpC;AACA;AACA;AACA,iBAAiBO,KAAK,IAAIA,KAAK,CAAC3B,KAAK,CAACY,KAAK,CAACC,IAAI;AAChD;AACA;AACA,CAAC;AAACkC,IAAA,GAPID,iBAAiB;AASvB,MAAME,OAAO,GAAG1D,MAAM,CAAC8B,GAAqB;AAC5C;AACA,WAAWO,KAAK,IAAI;EAChB,QAAQA,KAAK,CAACsB,IAAI;IAChB,KAAK,OAAO;MAAE,OAAOtB,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACQ,KAAK;IAC7C,KAAK,SAAS;MAAE,OAAOkB,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACU,OAAO;IACjD,KAAK,SAAS;MAAE,OAAOgB,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACS,OAAO;IACjD,KAAK,MAAM;MAAE,OAAOiB,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACE,SAAS;IAChD,KAAK,YAAY;MAAE,OAAOwB,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACG,MAAM;IACnD,KAAK,MAAM;MAAE,OAAOuB,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACO,aAAa;IACpD;MAAS,OAAOmB,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACM,IAAI;EACzC;AACF,CAAC;AACH;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GAhBIF,OAAO;AAkBb,MAAMG,cAAc,GAAG7D,MAAM,CAAC8B,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GALID,cAAc;AAOpB,MAAME,MAAM,GAAG/D,MAAM,CAACiD,IAAI;AAC1B,WAAWZ,KAAK,IAAIA,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACC,OAAO;AAC9C;AACA;AACA,CAAC;AAACoD,IAAA,GAJID,MAAM;AAMZ,MAAME,KAAK,GAAGjE,MAAM,CAACkE,KAAK;AAC1B;AACA;AACA;AACA,WAAW7B,KAAK,IAAIA,KAAK,CAAC3B,KAAK,CAACC,MAAM,CAACM,IAAI;AAC3C,iBAAiBoB,KAAK,IAAIA,KAAK,CAAC3B,KAAK,CAACY,KAAK,CAACC,IAAI;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAA4C,IAAA,GAdMF,KAAK;AAsBX,MAAMG,cAA6C,GAAGA,CAAC;EACrDC,QAAQ;EACRC,SAAS;EACTC,WAAW;EACXrB;AACF,CAAC,KAAK;EAAAsB,EAAA;EACJ,MAAM,CAACN,KAAK,EAAEO,QAAQ,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM6E,cAAc,GAAG3E,MAAM,CAAiB,IAAI,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAM;IAAA,IAAA6E,qBAAA;IACd,CAAAA,qBAAA,GAAAD,cAAc,CAACE,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;EAEd,MAAMU,YAAY,GAAIC,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIf,KAAK,CAACgB,IAAI,CAAC,CAAC,IAAIhC,SAAS,IAAI,CAACqB,WAAW,EAAE;MAC7CD,SAAS,CAACJ,KAAK,CAACgB,IAAI,CAAC,CAAC,CAAC;MACvBT,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,oBACElE,OAAA,CAAC+C,eAAe;IAAA6B,QAAA,gBACd5E,OAAA,CAACiD,iBAAiB;MAAA2B,QAAA,GACfd,QAAQ,CAACe,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B/E,OAAA,CAACmD,OAAO;QAAaC,IAAI,EAAE0B,OAAO,CAAC1B,IAAK;QAAAwB,QAAA,EACrCE,OAAO,CAACE;MAAO,GADJD,KAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACV,CAAC,EACDpB,WAAW,iBACVhE,OAAA,CAACmD,OAAO;QAACC,IAAI,EAAC,YAAY;QAAAwB,QAAA,EAAC;MAE3B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CACV,eACDpF,OAAA;QAAKqF,GAAG,EAAElB;MAAe;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAEpBpF,OAAA;MAAMsF,QAAQ,EAAEd,YAAa;MAAAI,QAAA,eAC3B5E,OAAA,CAACsD,cAAc;QAAAsB,QAAA,gBACb5E,OAAA,CAACwD,MAAM;UAAAoB,QAAA,EAAC;QAAgB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjCpF,OAAA,CAAC0D,KAAK;UACJN,IAAI,EAAC,MAAM;UACXmC,KAAK,EAAE5B,KAAM;UACb6B,QAAQ,EAAGf,CAAC,IAAKP,QAAQ,CAACO,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;UAC1CG,WAAW,EAAE/C,SAAS,GAAG,+CAA+C,GAAG,eAAgB;UAC3FgD,QAAQ,EAAE,CAAChD,SAAS,IAAIqB;QAAY;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEtB,CAAC;;AAED;AAAAnB,EAAA,CAtDMJ,cAA6C;AAAA+B,IAAA,GAA7C/B,cAA6C;AAuDnD,MAAMgC,cAAc,GAAGpG,MAAM,CAAC8B,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuE,IAAA,GAVID,cAAc;AAYpB,MAAME,SAAS,GAAGtG,MAAM,CAAC8B,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBO,KAAK,IAAIA,KAAK,CAAC3B,KAAK,CAACY,KAAK,CAACC,IAAI;AAChD;AACA;AACA,CAAC;AAACgF,IAAA,GAZID,SAAS;AAcf,MAAME,aAA2D,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACpF,IAAI,CAACA,QAAQ,EAAE;IACb,oBACElG,OAAA,CAAC6F,cAAc;MAAAjB,QAAA,eACb5E,OAAA;QAAKmG,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAzB,QAAA,gBACjD5E,OAAA;UAAA4E,QAAA,EAAI;QAAyB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClCpF,OAAA;UAAA4E,QAAA,EAAG;QAAuC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAErB;EAEA,oBACEpF,OAAA,CAAC6F,cAAc;IAAAjB,QAAA,gBACb5E,OAAA,CAAC+F,SAAS;MAAAnB,QAAA,gBACR5E,OAAA;QAAA4E,QAAA,GAAI,sBAAU,EAACsB,QAAQ,CAACI,YAAY,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KAAG;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9DpF,OAAA;QAAA4E,QAAA,gBAAG5E,OAAA;UAAA4E,QAAA,EAAQ;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACc,QAAQ,CAACM,WAAW;MAAA;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3DpF,OAAA;QAAA4E,QAAA,gBAAG5E,OAAA;UAAA4E,QAAA,EAAQ;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACqB,MAAM,CAACC,IAAI,CAACR,QAAQ,CAACS,QAAQ,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MAAA;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7EpF,OAAA;QAAA4E,QAAA,gBAAG5E,OAAA;UAAA4E,QAAA,EAAQ;QAAQ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACc,QAAQ,CAACW,OAAO,CAAChC,GAAG,CAACiC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAACH,IAAI,CAAC,IAAI,CAAC;MAAA;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/EpF,OAAA;QAAA4E,QAAA,gBAAG5E,OAAA;UAAA4E,QAAA,EAAQ;QAAW;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACc,QAAQ,CAACc,UAAU,EAAC,GAAC;MAAA;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,EAGX,CAAC,GAAG6B,KAAK,CAAC,EAAE,CAAC,CAAC,CAACpC,GAAG,CAAC,CAACqC,CAAC,EAAEC,CAAC,kBACvBnH,OAAA,CAACJ,MAAM,CAAC2B,GAAG;MAET4E,KAAK,EAAE;QACLiB,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACb9G,UAAU,EAAE0F,QAAQ,CAACqB,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,IAAI,SAAS;QACzDC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE;MACX,CAAE;MACFC,OAAO,EAAE;QACPC,CAAC,EAAE,CAAC,CAAC,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;QACjCC,CAAC,EAAE,CAAC,CAAC,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;QACjCE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACjB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC/BK,MAAM,EAAEC,QAAQ;QAChBC,IAAI,EAAE;MACR;IAAE,GAlBGlB,CAAC;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmBP,CACF,CAAC,eAEFpF,OAAA;MAAKmG,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEkC,SAAS,EAAE;MAAO,CAAE;MAAA1D,QAAA,gBACrD5E,OAAA;QAAA4E,QAAA,EAAI;MAA0B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCpF,OAAA;QAAA4E,QAAA,GAAG,eAAa,EAACsB,QAAQ,CAACqB,OAAO,CAACf,WAAW;MAAA;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDpF,OAAA;QAAA4E,QAAA,GAAG,YAAU,EAACsB,QAAQ,CAACqB,OAAO,CAACgB,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5B,IAAI,CAAC,IAAI,CAAC;MAAA;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAErB,CAAC;;AAED;AAAAqD,IAAA,GAxDMxC,aAA2D;AAyDjE,MAAMyC,GAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvJ,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAACqD,SAAS,EAAEmG,YAAY,CAAC,GAAGxJ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwE,QAAQ,EAAEiF,WAAW,CAAC,GAAGzJ,QAAQ,CAAoB,EAAE,CAAC;EAC/D,MAAM,CAAC0J,eAAe,EAAEC,kBAAkB,CAAC,GAAG3J,QAAQ,CAAuB,IAAI,CAAC;EAClF,MAAM,CAAC2C,MAAM,EAAEiH,SAAS,CAAC,GAAG5J,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC0E,WAAW,EAAEmF,cAAc,CAAC,GAAG7J,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8J,QAAQ,EAAEC,WAAW,CAAC,GAAG/J,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMgK,SAAS,GAAG9J,MAAM,CAAgB,IAAI,CAAC;;EAE7C;EACAD,SAAS,CAAC,MAAM;IACd,MAAMgK,SAAS,GAAGzJ,EAAE,CAAC0J,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG,uBAAuB,EAAE;MAC7GC,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS;IACrC,CAAC,CAAC;IAEFR,SAAS,CAACjF,OAAO,GAAGkF,SAAS;IAC7BV,SAAS,CAACU,SAAS,CAAC;;IAEpB;IACAA,SAAS,CAACQ,EAAE,CAAC,SAAS,EAAE,MAAM;MAC5BC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CnB,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,CAAC;IAEFS,SAAS,CAACQ,EAAE,CAAC,YAAY,EAAE,MAAM;MAC/BC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDnB,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;;IAEF;IACAS,SAAS,CAACQ,EAAE,CAAC,iBAAiB,EAAGjF,OAAwB,IAAK;MAC5DiE,WAAW,CAACmB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEpF,OAAO,CAAC,CAAC;IACzC,CAAC,CAAC;IAEFyE,SAAS,CAACQ,EAAE,CAAC,gBAAgB,EAAE,MAAM;MACnChB,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,CAAC;IAEFQ,SAAS,CAACQ,EAAE,CAAC,WAAW,EAAE,MAAM;MAC9Bb,SAAS,CAACgB,IAAI,IAAI,CAACA,IAAI,CAAC;IAC1B,CAAC,CAAC;;IAEF;IACAX,SAAS,CAACQ,EAAE,CAAC,gBAAgB,EAAG7D,QAAuB,IAAK;MAC1D8D,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE/D,QAAQ,CAAC;MACpD+C,kBAAkB,CAAC/C,QAAQ,CAAC;MAC5BiD,cAAc,CAAC,KAAK,CAAC;MACrBD,SAAS,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEF,OAAO,MAAM;MACXK,SAAS,CAACY,KAAK,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5K,SAAS,CAAC,MAAM;IACd,MAAM6K,WAAW,GAAGA,CAAA,KAAM;MACxBf,WAAW,CAACM,MAAM,CAACU,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,WAAW,CAAC,CAAC;IACbT,MAAM,CAACW,gBAAgB,CAAC,QAAQ,EAAEF,WAAW,CAAC;IAE9C,OAAO,MAAMT,MAAM,CAACY,mBAAmB,CAAC,QAAQ,EAAEH,WAAW,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,aAAa,GAAI7G,KAAa,IAAK;IACvC,IAAI,CAACiF,MAAM,IAAI,CAACjG,SAAS,EAAE;MACzBoG,WAAW,CAACmB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5B9G,IAAI,EAAE,OAAO;QACb4B,OAAO,EAAE,mDAAmD;QAC5DyF,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC,CAAC;MACH;IACF;;IAEA;IACA,MAAMC,KAAK,GAAGjH,KAAK,CAACgB,IAAI,CAAC,CAAC,CAACkG,KAAK,CAAC,GAAG,CAAC;IACrC,MAAMC,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC;IACxB,MAAMG,IAAI,GAAGH,KAAK,CAACpC,KAAK,CAAC,CAAC,CAAC,CAAC5B,IAAI,CAAC,GAAG,CAAC;;IAErC;IACAmC,WAAW,CAACmB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC5B9G,IAAI,EAAE,MAAM;MACZ4B,OAAO,EAAE,KAAKrB,KAAK,EAAE;MACrB8G,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIG,OAAO,CAACE,WAAW,CAAC,CAAC,KAAK,SAAS,IAAID,IAAI,EAAE;MAC/C5B,cAAc,CAAC,IAAI,CAAC;IACtB;;IAEA;IACAP,MAAM,CAACqC,IAAI,CAAC,eAAe,EAAE;MAAEH,OAAO;MAAEC;IAAK,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAGA,CAAC9H,IAAY,EAAE+H,IAAU,KAAK;IACxDnB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE7G,IAAI,EAAE+H,IAAI,CAAC;IAE1C,IAAI/H,IAAI,KAAK,cAAc,IAAI+H,IAAI,EAAE;MACnCpC,WAAW,CAACmB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5B9G,IAAI,EAAE,MAAM;QACZ4B,OAAO,EAAE,iBAAiBmG,IAAI,CAACC,MAAM,cAAc;QACnDX,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,oBACE3K,OAAA,CAACL,aAAa;IAACQ,KAAK,EAAEA,KAAM;IAAAyE,QAAA,gBAC1B5E,OAAA,CAACC,WAAW;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfpF,OAAA,CAACsB,YAAY;MAAAsD,QAAA,gBAEX5E,OAAA,CAAC6C,iBAAiB;QAAA+B,QAAA,EACf,CAAC,GAAGqC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACpC,GAAG,CAAC,CAACqC,CAAC,EAAEC,CAAC,kBACvBnH,OAAA,CAACJ,MAAM,CAAC2B,GAAG;UAET4E,KAAK,EAAE;YACLiB,QAAQ,EAAE,UAAU;YACpBC,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE,KAAK;YACb9G,UAAU,EAAEL,KAAK,CAACC,MAAM,CAACC,OAAO;YAChCoH,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX,CAAE;UACFC,OAAO,EAAE;YACPC,CAAC,EAAE,CAAC,CAAC,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG6B,MAAM,CAACU,UAAU,CAAC;YACzCtC,CAAC,EAAE,CAAC,CAAC,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG6B,MAAM,CAAC0B,WAAW,CAAC;YAC1C3D,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACrB,CAAE;UACFO,UAAU,EAAE;YACVC,QAAQ,EAAE,EAAE,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YACjCK,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE;UACR;QAAE,GAlBGlB,CAAC;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACe,CAAC,eAGpBpF,OAAA,CAACyB,MAAM;QACL6J,OAAO,EAAE;UAAEvD,CAAC,EAAE,CAAC,EAAE;UAAEL,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEI,CAAC,EAAE,CAAC;UAAEL,OAAO,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAtD,QAAA,eAE9B5E,OAAA,CAAC4B,KAAK;UAAAgD,QAAA,EAAC;QAAyB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAGTpF,OAAA,CAACgC,WAAW;QAACC,MAAM,EAAEA,MAAO;QAAA2C,QAAA,gBAE1B5E,OAAA,CAACmC,iBAAiB;UAChBmJ,OAAO,EAAE;YAAE1D,CAAC,EAAE,CAAC,GAAG;YAAEF,OAAO,EAAE;UAAE,CAAE;UACjCC,OAAO,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEF,OAAO,EAAE;UAAE,CAAE;UAC9BO,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEqD,KAAK,EAAE;UAAI,CAAE;UAAA3G,QAAA,eAE1C5E,OAAA,CAAC6D,cAAc;YACbC,QAAQ,EAAEA,QAAS;YACnBC,SAAS,EAAEyG,aAAc;YACzBxG,WAAW,EAAEA,WAAY;YACzBrB,SAAS,EAAEA;UAAU;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC,eAGpBpF,OAAA,CAACH,eAAe;UAAA+E,QAAA,EACb3C,MAAM,iBACLjC,OAAA,CAACqC,gBAAgB;YACfiJ,OAAO,EAAE;cAAE1D,CAAC,EAAE,GAAG;cAAEF,OAAO,EAAE,CAAC;cAAEM,KAAK,EAAE;YAAI,CAAE;YAC5CL,OAAO,EAAE;cAAEC,CAAC,EAAE,CAAC;cAAEF,OAAO,EAAE,CAAC;cAAEM,KAAK,EAAE;YAAE,CAAE;YACxCwD,IAAI,EAAE;cAAE5D,CAAC,EAAE,GAAG;cAAEF,OAAO,EAAE,CAAC;cAAEM,KAAK,EAAE;YAAI,CAAE;YACzCC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAtD,QAAA,eAE9B5E,OAAA,CAACiG,aAAa;cAACC,QAAQ,EAAE8C;YAAgB;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QACnB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAGbgE,QAAQ,IAAIJ,eAAe,iBAC1BhJ,OAAA;QAAKmG,KAAK,EAAE;UACViB,QAAQ,EAAE,OAAO;UACjBqE,MAAM,EAAE,MAAM;UACdC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbnL,UAAU,EAAE,oBAAoB;UAChCoL,OAAO,EAAE,MAAM;UACfnE,YAAY,EAAE,KAAK;UACnBoE,MAAM,EAAE,iCAAiC;UACzCC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;QACV,CAAE;QAAAnH,QAAA,gBACA5E,OAAA;UAAA4E,QAAA,GAAK,eAAG,EAACoE,eAAe,CAAC1C,YAAY,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KAAG;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChEpF,OAAA;UAAA4E,QAAA,GAAK,cAAY,EAACoE,eAAe,CAAChC,UAAU,EAAC,GAAC;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACN,eAGDpF,OAAA,CAACuC,SAAS;QAAAqC,QAAA,gBACR5E,OAAA,CAACyC,gBAAgB;UAACE,SAAS,EAAEA,SAAU;UAAAiC,QAAA,EACpCjC,SAAS,GAAG,WAAW,GAAG;QAAc;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACnBpF,OAAA;UAAA4E,QAAA,GAAM,YACM,EAACd,QAAQ,CAACkI,MAAM,EAAC,SACvB,EAAC/J,MAAM,GAAG,IAAI,GAAG,KAAK,EAAC,eACjB,EAAC+G,eAAe,GAAG,OAAO,GAAG,MAAM;QAAA;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAACuD,GAAA,CA1NID,GAAa;AAAAuD,IAAA,GAAbvD,GAAa;AA4NnB,eAAeA,GAAG;AAAC,IAAAxI,EAAA,EAAAsB,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAgC,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAyC,IAAA,EAAAwD,IAAA;AAAAC,YAAA,CAAAhM,EAAA;AAAAgM,YAAA,CAAA1K,GAAA;AAAA0K,YAAA,CAAAvK,GAAA;AAAAuK,YAAA,CAAAnK,GAAA;AAAAmK,YAAA,CAAAhK,GAAA;AAAAgK,YAAA,CAAA9J,GAAA;AAAA8J,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAtJ,GAAA;AAAAsJ,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAAhJ,IAAA;AAAAgJ,YAAA,CAAA7I,IAAA;AAAA6I,YAAA,CAAA3I,IAAA;AAAA2I,YAAA,CAAAzI,IAAA;AAAAyI,YAAA,CAAAtI,IAAA;AAAAsI,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}